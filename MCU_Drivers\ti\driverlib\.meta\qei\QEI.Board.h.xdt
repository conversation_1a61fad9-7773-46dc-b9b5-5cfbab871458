%%{
/*
 * Copyright (c) 2018 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== QEI.Board.h.xdt ========
 */

    let QEI = args[0]; /* passed by /ti/driverlib/templates/Board.c.xdt */
    let content = args[1];

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let instances = QEI.$instances;

    switch(content) {
        case "Define":
            printDefine();
            break;
        case "Declare":
            printDeclare();
            break;
        default:
            /* do nothing */
            return;
    }

%%}
%
% function printDefine() {
%   for (let i in instances) {
%       let inst = instances[i];
%       let flavor = inst.peripheral.$solution.peripheralName;
%       let nameStr = "#define "+inst.$name+"_INST"

/* Defines for `inst.$name` */
`nameStr.padEnd(40," ")+flavor.padStart(40," ")`
%           // add handler and IRQn number
%           let irqHandlerStr = "#define " + inst.$name + "_INST_IRQHandler"
%           let irqHandlerStr2 = flavor + "_IRQHandler";
%           let irqnStr = "#define " + inst.$name + "_INST_INT_IRQN";
%           let irqnStr2 = "(" + flavor + "_INT_IRQn)";
`irqHandlerStr.padEnd(40," ")+irqHandlerStr2.padStart(40," ")`
`irqnStr.padEnd(40," ")+irqnStr2.padStart(40," ")`
%
%       let maxPins = 2;
%       if(inst.enableIndexInput)
%           maxPins++;
%       for(let cc = 0;cc<maxPins;cc++){
%           let qeiPin = "";
%           let pinName = "ccp"+cc+"Pin";
%           let funcStr2_suffix = "_CCP"+cc;
%           let pinInterfaceName;
%           if(cc == 0) {
%                qeiPin = "PHA";
%                pinInterfaceName = "CCP0";
%           } else if (cc == 1) {
%               qeiPin = "PHB";
%               pinInterfaceName = "CCP1";
%           } else if(cc == 2) {
%               qeiPin = "IDX";
%               pinName = "idxPin";
%               funcStr2_suffix = "_"+qeiPin;
%               pinInterfaceName = "IDX"
%           }
%           let gpioStr = "GPIO_"+inst.$name+"_"+qeiPin;
%           let packagePin = inst.peripheral[pinName].$solution.packagePinName;
%           let pinCM = Common.getPinCM(packagePin,inst,pinInterfaceName);
%           let gpioName = system.deviceData.devicePins[packagePin].mux.muxSetting[0].peripheralPin.peripheralName;
%           let port = Common.getGPIOPortMultiPad(packagePin,inst,pinInterfaceName);
%           let gpioPin = Common.getGPIONumberMultiPad(packagePin,inst,pinInterfaceName);
/* Pin configuration defines for `inst.$name` `qeiPin` Pin */
% let portStr = "#define "+gpioStr+"_PORT";
`portStr.padEnd(40," ")+port.padStart(40, " ")`
% //#define `gpioStr`_PORT                              `port`
% let pinStr = "#define "+gpioStr+"_PIN";
% let pinStr2 = "DL_GPIO_PIN_"+gpioPin;
`pinStr.padEnd(40," ")+pinStr2.padStart(40," ")`
% //#define `gpioStr`_PIN                               DL_GPIO_PIN_`gpioPin`
% let iomuxStr = "#define "+gpioStr+"_IOMUX";
% let iomuxStr2 = "(IOMUX_PINCM"+pinCM+")";
`iomuxStr.padEnd(40," ")+iomuxStr2.padStart(40," ")`
% //#define `gpioStr`_IOMUX                             `iomux`
% let funcStr = "#define "+gpioStr+"_IOMUX_FUNC";
% let funcStr2 = "IOMUX_PINCM"+pinCM+"_PF_"+flavor+funcStr2_suffix;
`funcStr.padEnd(40, " ")+funcStr2.padStart(40, " ")`
% //#define `gpioStr`_IOMUX_FUNC                        IOMUX_PINCM`pinCM`_PF_`flav`_CCP`cc`
%       }
%   }
% } // end of function printDefine
%
% function printDeclare() {
%   let crossTriggerMainEn = false;
%   for (let i in instances) {
%       let inst = instances[i];
%       /* keep track if Cross Trigger is enabled as main for later code generation */
%       if(inst.crossTriggerEn && (inst.crossTriggerAuthority == "Main")){
%           crossTriggerMainEn = true;
%       }
void SYSCFG_DL_`inst.$name`_init(void);
%   }
%   if(crossTriggerMainEn){
void SYSCFG_DL_QEI_Cross_Trigger_init(void);
%   }
% }
