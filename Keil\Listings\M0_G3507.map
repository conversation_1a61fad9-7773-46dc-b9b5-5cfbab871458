Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to mpu6050.o(.text.mpu6050_init) for mpu6050_init
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to protocol.o(.text.protocol_init) for protocol_init
    main.o(.text.main) refers to key.o(.text.KEY_PROC) for KEY_PROC
    main.o(.text.main) refers to main.o(.text.OLED_Show_Proc) for OLED_Show_Proc
    main.o(.text.main) refers to main.o(.text.Protocol_Datas_Proc) for Protocol_Datas_Proc
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.NVIC_EnableIRQ_Init) refers to main.o(.text.NVIC_EnableIRQ_Init) for [Anonymous Symbol]
    main.o(.text.OLED_Show_Proc) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    main.o(.text.OLED_Show_Proc) refers to oled.o(.text.OLED_ShowBinNum) for OLED_ShowBinNum
    main.o(.text.OLED_Show_Proc) refers to oled.o(.text.OLED_ShowNum) for OLED_ShowNum
    main.o(.text.OLED_Show_Proc) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(.text.OLED_Show_Proc) refers to oled.o(.text.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(.text.OLED_Show_Proc) refers to f2d.o(.text) for __aeabi_f2d
    main.o(.text.OLED_Show_Proc) refers to oled.o(.text.OLED_ShowFloatNum) for OLED_ShowFloatNum
    main.o(.text.OLED_Show_Proc) refers to main.o(.data.OLED_View_Select) for OLED_View_Select
    main.o(.text.OLED_Show_Proc) refers to gw_gray.o(.bss.Huidu_Datas) for Huidu_Datas
    main.o(.text.OLED_Show_Proc) refers to gw_gray.o(.bss.Huidu_Sum) for Huidu_Sum
    main.o(.text.OLED_Show_Proc) refers to gw_gray.o(.bss.Huidu_Error) for Huidu_Error
    main.o(.text.OLED_Show_Proc) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    main.o(.text.OLED_Show_Proc) refers to encoder.o(.bss.Motor1_Speed) for Motor1_Speed
    main.o(.text.OLED_Show_Proc) refers to encoder.o(.bss.Motor2_Speed) for Motor2_Speed
    main.o(.text.OLED_Show_Proc) refers to main.o(.rodata.str1.1) for [Anonymous Symbol]
    main.o(.text.OLED_Show_Proc) refers to motor_ctrl.o(.data.pid_Turn) for pid_Turn
    main.o(.text.OLED_Show_Proc) refers to main.o(.bss.flag_1s) for flag_1s
    main.o(.text.OLED_Show_Proc) refers to main.o(.data.mode) for mode
    main.o(.text.OLED_Show_Proc) refers to motor_ctrl.o(.data.pid_Motor1_Speed) for pid_Motor1_Speed
    main.o(.text.OLED_Show_Proc) refers to oled.o(.text.OLED_Update) for OLED_Update
    main.o(.text.OLED_Show_Proc) refers to motor_ctrl.o(.data.pid_Gyro) for pid_Gyro
    main.o(.text.OLED_Show_Proc) refers to main.o(.bss.Target_Gyro) for Target_Gyro
    main.o(.text.OLED_Show_Proc) refers to mpu6050.o(.bss.Gyro_Z_Measeure) for Gyro_Z_Measeure
    main.o(.text.OLED_Show_Proc) refers to motor_ctrl.o(.data.pid_Angle) for pid_Angle
    main.o(.text.OLED_Show_Proc) refers to motor_ctrl.o(.data.pid_Distance) for pid_Distance
    main.o(.text.OLED_Show_Proc) refers to motor_ctrl.o(.data.pid_Motor2_Speed) for pid_Motor2_Speed
    main.o(.text.OLED_Show_Proc) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    main.o(.ARM.exidx.text.OLED_Show_Proc) refers to main.o(.text.OLED_Show_Proc) for [Anonymous Symbol]
    main.o(.text.Protocol_Datas_Proc) refers to main.o(.text.run_mode1) for run_mode1
    main.o(.text.Protocol_Datas_Proc) refers to main.o(.text.run_mode2) for run_mode2
    main.o(.text.Protocol_Datas_Proc) refers to main.o(.text.run_mode3) for run_mode3
    main.o(.text.Protocol_Datas_Proc) refers to main.o(.text.run_mode4_v2) for run_mode4_v2
    main.o(.text.Protocol_Datas_Proc) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(.text.Protocol_Datas_Proc) refers to protocol.o(.text.set_computer_value) for set_computer_value
    main.o(.text.Protocol_Datas_Proc) refers to protocol.o(.text.receiving_process) for receiving_process
    main.o(.text.Protocol_Datas_Proc) refers to main.o(.bss.Car_Mode) for Car_Mode
    main.o(.text.Protocol_Datas_Proc) refers to motor_ctrl.o(.bss.Distance_PID_Flag) for Distance_PID_Flag
    main.o(.text.Protocol_Datas_Proc) refers to motor_ctrl.o(.data.Turn_PID_Flag) for Turn_PID_Flag
    main.o(.text.Protocol_Datas_Proc) refers to motor_ctrl.o(.bss.Gyro_PID_Flag) for Gyro_PID_Flag
    main.o(.text.Protocol_Datas_Proc) refers to motor_ctrl.o(.bss.Angle_PID_Flag) for Angle_PID_Flag
    main.o(.text.Protocol_Datas_Proc) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    main.o(.text.Protocol_Datas_Proc) refers to mpu6050.o(.bss.Gyro_Z_Measeure) for Gyro_Z_Measeure
    main.o(.text.Protocol_Datas_Proc) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    main.o(.text.Protocol_Datas_Proc) refers to gw_gray.o(.bss.Huidu_Error) for Huidu_Error
    main.o(.text.Protocol_Datas_Proc) refers to protocol.o(.data.Set_Motor_Param_Select) for Set_Motor_Param_Select
    main.o(.text.Protocol_Datas_Proc) refers to encoder.o(.bss.Motor2_Speed) for Motor2_Speed
    main.o(.text.Protocol_Datas_Proc) refers to encoder.o(.bss.Motor1_Speed) for Motor1_Speed
    main.o(.text.Protocol_Datas_Proc) refers to main.o(.data.mode) for mode
    main.o(.ARM.exidx.text.Protocol_Datas_Proc) refers to main.o(.text.Protocol_Datas_Proc) for [Anonymous Symbol]
    main.o(.text.TIMA1_IRQHandler) refers to key.o(.text.Key_Read) for Key_Read
    main.o(.text.TIMA1_IRQHandler) refers to mpu6050.o(.text.AHRS_Geteuler) for AHRS_Geteuler
    main.o(.text.TIMA1_IRQHandler) refers to gw_gray.o(.text.Huidu_Read) for Huidu_Read
    main.o(.text.TIMA1_IRQHandler) refers to gw_gray.o(.text.Huidu_Proc) for Huidu_Proc
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.text.PID_Calculate) for PID_Calculate
    main.o(.text.TIMA1_IRQHandler) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    main.o(.text.TIMA1_IRQHandler) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    main.o(.text.TIMA1_IRQHandler) refers to fcmp.o(i._feq) for __aeabi_fcmpeq
    main.o(.text.TIMA1_IRQHandler) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(.text.TIMA1_IRQHandler) refers to f2d.o(.text) for __aeabi_f2d
    main.o(.text.TIMA1_IRQHandler) refers to dmul.o(.text) for __aeabi_dmul
    main.o(.text.TIMA1_IRQHandler) refers to d2f.o(.text) for __aeabi_d2f
    main.o(.text.TIMA1_IRQHandler) refers to encoder.o(.text.MEASURE_MOTORS_SPEED) for MEASURE_MOTORS_SPEED
    main.o(.text.TIMA1_IRQHandler) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.text.SET_MOTORS_SPEED) for SET_MOTORS_SPEED
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.TIMA1_IRQHandler.count_10ms) for [Anonymous Symbol]
    main.o(.text.TIMA1_IRQHandler) refers to gw_gray.o(.bss.Huidu_Datas) for Huidu_Datas
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.bss.Angle_PID_Flag) for Angle_PID_Flag
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.bss.Gyro_PID_Flag) for Gyro_PID_Flag
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Target_Angle) for Target_Angle
    main.o(.text.TIMA1_IRQHandler) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.pid_Angle) for pid_Angle
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Target_Gyro) for Target_Gyro
    main.o(.text.TIMA1_IRQHandler) refers to mpu6050.o(.bss.Gyro_Z_Measeure) for Gyro_Z_Measeure
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.pid_Gyro) for pid_Gyro
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.bss.Distance_PID_Flag) for Distance_PID_Flag
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Target_Distance) for Target_Distance
    main.o(.text.TIMA1_IRQHandler) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.pid_Distance) for pid_Distance
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Motor1_Target_Speed) for Motor1_Target_Speed
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.Turn_PID_Flag) for Turn_PID_Flag
    main.o(.text.TIMA1_IRQHandler) refers to gw_gray.o(.bss.Huidu_Target) for Huidu_Target
    main.o(.text.TIMA1_IRQHandler) refers to gw_gray.o(.bss.Huidu_Error) for Huidu_Error
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.pid_Turn) for pid_Turn
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Target_ChaSu) for Target_ChaSu
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.Motor2_Target_Speed) for Motor2_Target_Speed
    main.o(.text.TIMA1_IRQHandler) refers to encoder.o(.bss.Motor1_Speed) for Motor1_Speed
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.pid_Motor1_Speed) for pid_Motor1_Speed
    main.o(.text.TIMA1_IRQHandler) refers to encoder.o(.bss.Motor2_Speed) for Motor2_Speed
    main.o(.text.TIMA1_IRQHandler) refers to motor_ctrl.o(.data.pid_Motor2_Speed) for pid_Motor2_Speed
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.TIMA1_IRQHandler.count_100ms) for [Anonymous Symbol]
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.flag_1s) for flag_1s
    main.o(.text.TIMA1_IRQHandler) refers to main.o(.bss.TIMA1_IRQHandler.t) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.TIMA1_IRQHandler) refers to main.o(.text.TIMA1_IRQHandler) for [Anonymous Symbol]
    main.o(.text.run_mode2) refers to oled.o(.text.OLED_Show_String) for OLED_Show_String
    main.o(.text.run_mode2) refers to delay.o(.text.delay_ms) for delay_ms
    main.o(.text.run_mode2) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    main.o(.text.run_mode2) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    main.o(.text.run_mode2) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    main.o(.text.run_mode2) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    main.o(.text.run_mode2) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    main.o(.text.run_mode2) refers to main.o(.bss.key_val) for key_val
    main.o(.text.run_mode2) refers to key.o(.bss.Key) for Key
    main.o(.text.run_mode2) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    main.o(.text.run_mode2) refers to main.o(.bss.stop_flag) for stop_flag
    main.o(.text.run_mode2) refers to main.o(.bss.start_flag) for start_flag
    main.o(.text.run_mode2) refers to main.o(.bss.flag_1s) for flag_1s
    main.o(.text.run_mode2) refers to main.o(.bss.semicycle_num) for semicycle_num
    main.o(.text.run_mode2) refers to gw_gray.o(.bss.Huidu_Datas) for Huidu_Datas
    main.o(.text.run_mode2) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    main.o(.text.run_mode2) refers to main.o(.bss.total_distant_cnt) for total_distant_cnt
    main.o(.text.run_mode2) refers to motor_ctrl.o(.bss.Angle_PID_Flag) for Angle_PID_Flag
    main.o(.text.run_mode2) refers to motor_ctrl.o(.bss.Distance_PID_Flag) for Distance_PID_Flag
    main.o(.text.run_mode2) refers to motor_ctrl.o(.data.Turn_PID_Flag) for Turn_PID_Flag
    main.o(.text.run_mode2) refers to main.o(.bss.Target_Angle) for Target_Angle
    main.o(.text.run_mode2) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    main.o(.text.run_mode2) refers to main.o(.rodata.str1.1) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.run_mode2) refers to main.o(.text.run_mode2) for [Anonymous Symbol]
    main.o(.text.run_mode3) refers to oled.o(.text.OLED_Show_String) for OLED_Show_String
    main.o(.text.run_mode3) refers to delay.o(.text.delay_ms) for delay_ms
    main.o(.text.run_mode3) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    main.o(.text.run_mode3) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    main.o(.text.run_mode3) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    main.o(.text.run_mode3) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    main.o(.text.run_mode3) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    main.o(.text.run_mode3) refers to main.o(.bss.key_val) for key_val
    main.o(.text.run_mode3) refers to key.o(.bss.Key) for Key
    main.o(.text.run_mode3) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    main.o(.text.run_mode3) refers to main.o(.bss.stop_flag) for stop_flag
    main.o(.text.run_mode3) refers to main.o(.bss.start_flag) for start_flag
    main.o(.text.run_mode3) refers to main.o(.bss.run_mode3.num) for [Anonymous Symbol]
    main.o(.text.run_mode3) refers to main.o(.bss.flag_1s) for flag_1s
    main.o(.text.run_mode3) refers to main.o(.bss.semicycle_num) for semicycle_num
    main.o(.text.run_mode3) refers to gw_gray.o(.bss.Huidu_Datas) for Huidu_Datas
    main.o(.text.run_mode3) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    main.o(.text.run_mode3) refers to main.o(.bss.Target_Gyro) for Target_Gyro
    main.o(.text.run_mode3) refers to motor_ctrl.o(.bss.Angle_PID_Flag) for Angle_PID_Flag
    main.o(.text.run_mode3) refers to motor_ctrl.o(.bss.Distance_PID_Flag) for Distance_PID_Flag
    main.o(.text.run_mode3) refers to motor_ctrl.o(.data.Turn_PID_Flag) for Turn_PID_Flag
    main.o(.text.run_mode3) refers to motor_ctrl.o(.bss.Gyro_PID_Flag) for Gyro_PID_Flag
    main.o(.text.run_mode3) refers to main.o(.bss.total_distant_cnt) for total_distant_cnt
    main.o(.text.run_mode3) refers to main.o(.bss.Target_Distance) for Target_Distance
    main.o(.text.run_mode3) refers to main.o(.rodata.str1.1) for [Anonymous Symbol]
    main.o(.text.run_mode3) refers to main.o(.bss.Target_Angle) for Target_Angle
    main.o(.text.run_mode3) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    main.o(.ARM.exidx.text.run_mode3) refers to main.o(.text.run_mode3) for [Anonymous Symbol]
    main.o(.text.run_mode1) refers to oled.o(.text.OLED_Show_String) for OLED_Show_String
    main.o(.text.run_mode1) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    main.o(.text.run_mode1) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    main.o(.text.run_mode1) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    main.o(.text.run_mode1) refers to delay.o(.text.delay_ms) for delay_ms
    main.o(.text.run_mode1) refers to main.o(.bss.key_val) for key_val
    main.o(.text.run_mode1) refers to key.o(.bss.Key) for Key
    main.o(.text.run_mode1) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    main.o(.text.run_mode1) refers to gw_gray.o(.bss.Huidu_Datas) for Huidu_Datas
    main.o(.text.run_mode1) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    main.o(.text.run_mode1) refers to motor_ctrl.o(.bss.Distance_PID_Flag) for Distance_PID_Flag
    main.o(.text.run_mode1) refers to motor_ctrl.o(.bss.Angle_PID_Flag) for Angle_PID_Flag
    main.o(.text.run_mode1) refers to motor_ctrl.o(.data.Turn_PID_Flag) for Turn_PID_Flag
    main.o(.text.run_mode1) refers to main.o(.bss.stop_flag) for stop_flag
    main.o(.text.run_mode1) refers to main.o(.bss.Target_Angle) for Target_Angle
    main.o(.ARM.exidx.text.run_mode1) refers to main.o(.text.run_mode1) for [Anonymous Symbol]
    main.o(.text.run_mode4_v2) refers to oled.o(.text.OLED_Show_String) for OLED_Show_String
    main.o(.text.run_mode4_v2) refers to delay.o(.text.delay_ms) for delay_ms
    main.o(.text.run_mode4_v2) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    main.o(.text.run_mode4_v2) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    main.o(.text.run_mode4_v2) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    main.o(.text.run_mode4_v2) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    main.o(.text.run_mode4_v2) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    main.o(.text.run_mode4_v2) refers to main.o(.bss.key_val) for key_val
    main.o(.text.run_mode4_v2) refers to key.o(.bss.Key) for Key
    main.o(.text.run_mode4_v2) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    main.o(.text.run_mode4_v2) refers to main.o(.bss.stop_flag) for stop_flag
    main.o(.text.run_mode4_v2) refers to main.o(.bss.start_flag) for start_flag
    main.o(.text.run_mode4_v2) refers to main.o(.bss.run_mode4_v2.num) for [Anonymous Symbol]
    main.o(.text.run_mode4_v2) refers to main.o(.bss.flag_1s) for flag_1s
    main.o(.text.run_mode4_v2) refers to main.o(.bss.semicycle_num) for semicycle_num
    main.o(.text.run_mode4_v2) refers to gw_gray.o(.bss.Huidu_Datas) for Huidu_Datas
    main.o(.text.run_mode4_v2) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    main.o(.text.run_mode4_v2) refers to main.o(.bss.Target_Gyro) for Target_Gyro
    main.o(.text.run_mode4_v2) refers to motor_ctrl.o(.bss.Angle_PID_Flag) for Angle_PID_Flag
    main.o(.text.run_mode4_v2) refers to motor_ctrl.o(.bss.Distance_PID_Flag) for Distance_PID_Flag
    main.o(.text.run_mode4_v2) refers to motor_ctrl.o(.data.Turn_PID_Flag) for Turn_PID_Flag
    main.o(.text.run_mode4_v2) refers to motor_ctrl.o(.bss.Gyro_PID_Flag) for Gyro_PID_Flag
    main.o(.text.run_mode4_v2) refers to main.o(.bss.total_distant_cnt) for total_distant_cnt
    main.o(.text.run_mode4_v2) refers to main.o(.bss.Target_Distance) for Target_Distance
    main.o(.text.run_mode4_v2) refers to main.o(.rodata.str1.1) for [Anonymous Symbol]
    main.o(.text.run_mode4_v2) refers to main.o(.bss.Target_Angle) for Target_Angle
    main.o(.text.run_mode4_v2) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    main.o(.ARM.exidx.text.run_mode4_v2) refers to main.o(.text.run_mode4_v2) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to usart.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to main.o(.text.TIMA1_IRQHandler) for TIMA1_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    delay.o(.text.delay_ms) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    delay.o(.ARM.exidx.text.delay_ms) refers to delay.o(.text.delay_ms) for [Anonymous Symbol]
    delay.o(.text.delay_us) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    delay.o(.ARM.exidx.text.delay_us) refers to delay.o(.text.delay_us) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.uart0_send_char) refers to usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.uart0_send_string) refers to usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    usart.o(.text.usart0_send_byte) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.usart0_send_byte) refers to usart.o(.text.usart0_send_byte) for [Anonymous Symbol]
    usart.o(.text.usart0_send_bytes) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.usart0_send_bytes) refers to usart.o(.text.usart0_send_bytes) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_Transmit) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to usart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    usart.o(.text.fputc) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    usart.o(.text.JustFloat_SendArray) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.JustFloat_SendArray) refers to usart.o(.text.JustFloat_SendArray) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.Float_to_Byte) refers to usart.o(.text.Float_to_Byte) for [Anonymous Symbol]
    usart.o(.text.JustFloat_Test) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.JustFloat_Test) refers to usart.o(.text.JustFloat_Test) for [Anonymous Symbol]
    usart.o(.text.vofa_sendData) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    usart.o(.ARM.exidx.text.vofa_sendData) refers to usart.o(.text.vofa_sendData) for [Anonymous Symbol]
    usart.o(.text.UART0_IRQHandler) refers to protocol.o(.text.protocol_data_recv) for protocol_data_recv
    usart.o(.ARM.exidx.text.UART0_IRQHandler) refers to usart.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    key.o(.text.Key_Read) refers to key.o(.bss.Key) for Key
    key.o(.ARM.exidx.text.Key_Read) refers to key.o(.text.Key_Read) for [Anonymous Symbol]
    key.o(.text.KEY_PROC) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    key.o(.text.KEY_PROC) refers to delay.o(.text.delay_ms) for delay_ms
    key.o(.text.KEY_PROC) refers to key.o(.bss.Key) for Key
    key.o(.text.KEY_PROC) refers to main.o(.data.OLED_View_Select) for OLED_View_Select
    key.o(.text.KEY_PROC) refers to main.o(.data.mode) for mode
    key.o(.text.KEY_PROC) refers to motor_ctrl.o(.bss.MOTOR1_ENABLE_FLAG) for MOTOR1_ENABLE_FLAG
    key.o(.text.KEY_PROC) refers to motor_ctrl.o(.bss.MOTOR2_ENABLE_FLAG) for MOTOR2_ENABLE_FLAG
    key.o(.text.KEY_PROC) refers to main.o(.bss.key_val) for key_val
    key.o(.ARM.exidx.text.KEY_PROC) refers to key.o(.text.KEY_PROC) for [Anonymous Symbol]
    oled.o(.text.OLED_ColorTurn) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_ColorTurn) refers to oled.o(.text.OLED_ColorTurn) for [Anonymous Symbol]
    oled.o(.text.OLED_WR_Byte) refers to delay.o(.text.delay_us) for delay_us
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.I2C_WaitAck) for I2C_WaitAck
    oled.o(.ARM.exidx.text.OLED_WR_Byte) refers to oled.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    oled.o(.text.OLED_DisplayTurn) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_DisplayTurn) refers to oled.o(.text.OLED_DisplayTurn) for [Anonymous Symbol]
    oled.o(.text.I2C_WaitAck) refers to delay.o(.text.delay_us) for delay_us
    oled.o(.ARM.exidx.text.I2C_WaitAck) refers to oled.o(.text.I2C_WaitAck) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteData) refers to delay.o(.text.delay_us) for delay_us
    oled.o(.ARM.exidx.text.OLED_WriteData) refers to oled.o(.text.OLED_WriteData) for [Anonymous Symbol]
    oled.o(.text.OLED_DisPlay_On) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_DisPlay_On) refers to oled.o(.text.OLED_DisPlay_On) for [Anonymous Symbol]
    oled.o(.text.OLED_DisPlay_Off) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_DisPlay_Off) refers to oled.o(.text.OLED_DisPlay_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Refresh) refers to delay.o(.text.delay_us) for delay_us
    oled.o(.text.OLED_Refresh) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Refresh) refers to oled.o(.text.I2C_WaitAck) for I2C_WaitAck
    oled.o(.text.OLED_Refresh) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Refresh) refers to oled.o(.text.OLED_Refresh) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_Refresh) for OLED_Refresh
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawLine) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawLine) refers to oled.o(.text.OLED_DrawLine) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawRectangle) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawRectangle) refers to oled.o(.text.OLED_DrawRectangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawCircle) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawCircle) refers to oled.o(.text.OLED_DrawCircle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawEllipse) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(.text.OLED_DrawEllipse) refers to daddsub.o(.text) for __aeabi_dadd
    oled.o(.text.OLED_DrawEllipse) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(.text.OLED_DrawEllipse) refers to d2f.o(.text) for __aeabi_d2f
    oled.o(.text.OLED_DrawEllipse) refers to dcmp.o(i._dleq) for __aeabi_dcmple
    oled.o(.text.OLED_DrawEllipse) refers to dcmp.o(i._dgr) for __aeabi_dcmpgt
    oled.o(.text.OLED_DrawEllipse) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    oled.o(.text.OLED_DrawEllipse) refers to fflti.o(.text) for __aeabi_i2f
    oled.o(.text.OLED_DrawEllipse) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(.text.OLED_DrawEllipse) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    oled.o(.text.OLED_DrawEllipse) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawEllipse) refers to oled.o(.text.OLED_DrawEllipse) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowChar) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Pow) refers to oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_ShowNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_ShowNum) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_ShowSignedNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_ShowSignedNum) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled.o(.ARM.exidx.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowSignedNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_ShowHexNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.ARM.exidx.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowHexNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowBinNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowBinNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowFloatNum) refers to dcmp.o(i._dleq) for __aeabi_dcmple
    oled.o(.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_ShowFloatNum) refers to dcmp.o(i._deq) for __aeabi_dcmpeq
    oled.o(.text.OLED_ShowFloatNum) refers to dfixui.o(.text) for __aeabi_d2uiz
    oled.o(.text.OLED_ShowFloatNum) refers to dflti.o(.text) for __aeabi_ui2d
    oled.o(.text.OLED_ShowFloatNum) refers to daddsub.o(.text) for __aeabi_dsub
    oled.o(.text.OLED_ShowFloatNum) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(.text.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(.text.OLED_ShowFloatNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_ShowFloatNum) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled.o(.ARM.exidx.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowFloatNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_ShowChinese) refers to oled.o(.text.OLED_ShowChinese) for [Anonymous Symbol]
    oled.o(.text.OLED_ScrollDisplay) refers to oled.o(.text.OLED_Refresh) for OLED_Refresh
    oled.o(.text.OLED_ScrollDisplay) refers to oled.o(.text.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(.text.OLED_ScrollDisplay) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_ScrollDisplay) refers to oled.o(.text.OLED_ScrollDisplay) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowPicture) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_ShowPicture) refers to oled.o(.text.OLED_ShowPicture) for [Anonymous Symbol]
    oled.o(.text.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(.text.OLED_Printf) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_Printf) refers to oled.o(.text.OLED_Printf) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_GPIO_Init) refers to oled.o(.text.OLED_GPIO_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Refresh) for OLED_Refresh
    oled.o(.text.OLED_Init) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_Refresh) for OLED_Refresh
    oled.o(.ARM.exidx.text.OLED_Update) refers to oled.o(.text.OLED_Update) for [Anonymous Symbol]
    oled.o(.text.OLED_Show_String) refers to oled.o(.text.OLED_Refresh) for OLED_Refresh
    oled.o(.text.OLED_Show_String) refers to oled.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_Show_String) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Show_String) refers to oled.o(.text.OLED_Show_String) for [Anonymous Symbol]
    oled.o(.text.OLED_DisplayLine) refers to oled.o(.text.OLED_Show_String) for OLED_Show_String
    oled.o(.ARM.exidx.text.OLED_DisplayLine) refers to oled.o(.text.OLED_DisplayLine) for [Anonymous Symbol]
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Motor1_Encoder_Value) for Motor1_Encoder_Value
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Motor2_Encoder_Value) for Motor2_Encoder_Value
    encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.Motor1_Get_Speed) refers to dflti.o(.text) for __aeabi_i2d
    encoder.o(.text.Motor1_Get_Speed) refers to ddiv.o(.text) for __aeabi_ddiv
    encoder.o(.text.Motor1_Get_Speed) refers to dmul.o(.text) for __aeabi_dmul
    encoder.o(.text.Motor1_Get_Speed) refers to d2f.o(.text) for __aeabi_d2f
    encoder.o(.text.Motor1_Get_Speed) refers to f2d.o(.text) for __aeabi_f2d
    encoder.o(.text.Motor1_Get_Speed) refers to encoder.o(.bss.Motor1_Encoder_Value) for Motor1_Encoder_Value
    encoder.o(.text.Motor1_Get_Speed) refers to encoder.o(.bss.Motor1_Speed) for Motor1_Speed
    encoder.o(.ARM.exidx.text.Motor1_Get_Speed) refers to encoder.o(.text.Motor1_Get_Speed) for [Anonymous Symbol]
    encoder.o(.text.Motor2_Get_Speed) refers to dflti.o(.text) for __aeabi_i2d
    encoder.o(.text.Motor2_Get_Speed) refers to ddiv.o(.text) for __aeabi_ddiv
    encoder.o(.text.Motor2_Get_Speed) refers to dmul.o(.text) for __aeabi_dmul
    encoder.o(.text.Motor2_Get_Speed) refers to d2f.o(.text) for __aeabi_d2f
    encoder.o(.text.Motor2_Get_Speed) refers to f2d.o(.text) for __aeabi_f2d
    encoder.o(.text.Motor2_Get_Speed) refers to encoder.o(.bss.Motor2_Encoder_Value) for Motor2_Encoder_Value
    encoder.o(.text.Motor2_Get_Speed) refers to encoder.o(.bss.Motor2_Speed) for Motor2_Speed
    encoder.o(.ARM.exidx.text.Motor2_Get_Speed) refers to encoder.o(.text.Motor2_Get_Speed) for [Anonymous Symbol]
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to dflti.o(.text) for __aeabi_i2d
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to ddiv.o(.text) for __aeabi_ddiv
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to dmul.o(.text) for __aeabi_dmul
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to d2f.o(.text) for __aeabi_d2f
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to f2d.o(.text) for __aeabi_f2d
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to daddsub.o(.text) for __aeabi_dadd
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Motor1_Encoder_Value) for Motor1_Encoder_Value
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Motor2_Encoder_Value) for Motor2_Encoder_Value
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Motor1_Speed) for Motor1_Speed
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Motor2_Speed) for Motor2_Speed
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Motor1_Lucheng) for Motor1_Lucheng
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Motor2_Lucheng) for Motor2_Lucheng
    encoder.o(.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.bss.Measure_Distance) for Measure_Distance
    encoder.o(.ARM.exidx.text.MEASURE_MOTORS_SPEED) refers to encoder.o(.text.MEASURE_MOTORS_SPEED) for [Anonymous Symbol]
    motor_ctrl.o(.text.PID_Limit) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    motor_ctrl.o(.text.PID_Limit) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    motor_ctrl.o(.ARM.exidx.text.PID_Limit) refers to motor_ctrl.o(.text.PID_Limit) for [Anonymous Symbol]
    motor_ctrl.o(.text.PID_Calculate) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    motor_ctrl.o(.text.PID_Calculate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor_ctrl.o(.text.PID_Calculate) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    motor_ctrl.o(.text.PID_Calculate) refers to fflti.o(.text) for __aeabi_ui2f
    motor_ctrl.o(.text.PID_Calculate) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    motor_ctrl.o(.text.PID_Calculate) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    motor_ctrl.o(.ARM.exidx.text.PID_Calculate) refers to motor_ctrl.o(.text.PID_Calculate) for [Anonymous Symbol]
    motor_ctrl.o(.ARM.exidx.text.Set_PID_Param) refers to motor_ctrl.o(.text.Set_PID_Param) for [Anonymous Symbol]
    motor_ctrl.o(.ARM.exidx.text.PWM_Limit) refers to motor_ctrl.o(.text.PWM_Limit) for [Anonymous Symbol]
    motor_ctrl.o(.text.Set_Motor1_PWM) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_ctrl.o(.ARM.exidx.text.Set_Motor1_PWM) refers to motor_ctrl.o(.text.Set_Motor1_PWM) for [Anonymous Symbol]
    motor_ctrl.o(.text.Set_Motor2_PWM) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_ctrl.o(.ARM.exidx.text.Set_Motor2_PWM) refers to motor_ctrl.o(.text.Set_Motor2_PWM) for [Anonymous Symbol]
    motor_ctrl.o(.text.Set_Motor1_Speed) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_ctrl.o(.text.Set_Motor1_Speed) refers to motor_ctrl.o(.bss.MOTOR1_ENABLE_FLAG) for MOTOR1_ENABLE_FLAG
    motor_ctrl.o(.text.Set_Motor1_Speed) refers to motor_ctrl.o(.data.pid_Motor1_Speed) for pid_Motor1_Speed
    motor_ctrl.o(.ARM.exidx.text.Set_Motor1_Speed) refers to motor_ctrl.o(.text.Set_Motor1_Speed) for [Anonymous Symbol]
    motor_ctrl.o(.text.Set_Motor2_Speed) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_ctrl.o(.text.Set_Motor2_Speed) refers to motor_ctrl.o(.bss.MOTOR2_ENABLE_FLAG) for MOTOR2_ENABLE_FLAG
    motor_ctrl.o(.text.Set_Motor2_Speed) refers to motor_ctrl.o(.data.pid_Motor2_Speed) for pid_Motor2_Speed
    motor_ctrl.o(.ARM.exidx.text.Set_Motor2_Speed) refers to motor_ctrl.o(.text.Set_Motor2_Speed) for [Anonymous Symbol]
    motor_ctrl.o(.text.SET_MOTORS_SPEED) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_ctrl.o(.text.SET_MOTORS_SPEED) refers to motor_ctrl.o(.bss.MOTOR1_ENABLE_FLAG) for MOTOR1_ENABLE_FLAG
    motor_ctrl.o(.text.SET_MOTORS_SPEED) refers to motor_ctrl.o(.data.pid_Motor1_Speed) for pid_Motor1_Speed
    motor_ctrl.o(.text.SET_MOTORS_SPEED) refers to motor_ctrl.o(.bss.MOTOR2_ENABLE_FLAG) for MOTOR2_ENABLE_FLAG
    motor_ctrl.o(.text.SET_MOTORS_SPEED) refers to motor_ctrl.o(.data.pid_Motor2_Speed) for pid_Motor2_Speed
    motor_ctrl.o(.ARM.exidx.text.SET_MOTORS_SPEED) refers to motor_ctrl.o(.text.SET_MOTORS_SPEED) for [Anonymous Symbol]
    protocol.o(.ARM.exidx.text.check_sum) refers to protocol.o(.text.check_sum) for [Anonymous Symbol]
    protocol.o(.text.protocol_data_recv) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    protocol.o(.text.protocol_data_recv) refers to protocol.o(.bss.parser) for [Anonymous Symbol]
    protocol.o(.ARM.exidx.text.protocol_data_recv) refers to protocol.o(.text.protocol_data_recv) for [Anonymous Symbol]
    protocol.o(.text.protocol_init) refers to protocol.o(.bss.parser) for [Anonymous Symbol]
    protocol.o(.text.protocol_init) refers to protocol.o(.bss.recv_buf) for [Anonymous Symbol]
    protocol.o(.ARM.exidx.text.protocol_init) refers to protocol.o(.text.protocol_init) for [Anonymous Symbol]
    protocol.o(.text.receiving_process) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.text.Set_PID_Param) for Set_PID_Param
    protocol.o(.text.receiving_process) refers to fflti.o(.text) for __aeabi_i2f
    protocol.o(.text.receiving_process) refers to protocol.o(.bss.parser) for [Anonymous Symbol]
    protocol.o(.text.receiving_process) refers to main.o(.bss.Car_Mode) for Car_Mode
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.bss.MOTOR2_ENABLE_FLAG) for MOTOR2_ENABLE_FLAG
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.bss.MOTOR1_ENABLE_FLAG) for MOTOR1_ENABLE_FLAG
    protocol.o(.text.receiving_process) refers to protocol.o(.data.Set_Motor_Param_Select) for Set_Motor_Param_Select
    protocol.o(.text.receiving_process) refers to main.o(.bss.Target_Angle) for Target_Angle
    protocol.o(.text.receiving_process) refers to main.o(.bss.Target_Gyro) for Target_Gyro
    protocol.o(.text.receiving_process) refers to main.o(.bss.Target_Distance) for Target_Distance
    protocol.o(.text.receiving_process) refers to main.o(.bss.Basic_Speed) for Basic_Speed
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.data.pid_Angle) for pid_Angle
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.data.pid_Gyro) for pid_Gyro
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.data.pid_Distance) for pid_Distance
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.data.pid_Turn) for pid_Turn
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.data.pid_Motor2_Speed) for pid_Motor2_Speed
    protocol.o(.text.receiving_process) refers to motor_ctrl.o(.data.pid_Motor1_Speed) for pid_Motor1_Speed
    protocol.o(.ARM.exidx.text.receiving_process) refers to protocol.o(.text.receiving_process) for [Anonymous Symbol]
    protocol.o(.text.set_computer_value) refers to usart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    protocol.o(.text.set_computer_value) refers to protocol.o(.bss.set_computer_value.set_packet) for [Anonymous Symbol]
    protocol.o(.ARM.exidx.text.set_computer_value) refers to protocol.o(.text.set_computer_value) for [Anonymous Symbol]
    gw_gray.o(.ARM.exidx.text.Huidu_Read) refers to gw_gray.o(.text.Huidu_Read) for [Anonymous Symbol]
    gw_gray.o(.text.Huidu_Proc) refers to gw_gray.o(.bss.Huidu_Sum) for Huidu_Sum
    gw_gray.o(.text.Huidu_Proc) refers to gw_gray.o(.bss.Huidu_Proc.huidu_lasterror) for [Anonymous Symbol]
    gw_gray.o(.text.Huidu_Proc) refers to gw_gray.o(.bss.Huidu_Error) for Huidu_Error
    gw_gray.o(.ARM.exidx.text.Huidu_Proc) refers to gw_gray.o(.text.Huidu_Proc) for [Anonymous Symbol]
    mpu6050.o(.text.i2c0_write_n_byte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    mpu6050.o(.text.i2c0_write_n_byte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    mpu6050.o(.text.i2c0_write_n_byte) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    mpu6050.o(.text.i2c0_write_n_byte) refers to mpu6050.o(.bss.i2c0_write_n_byte.temp_reg_dddr) for [Anonymous Symbol]
    mpu6050.o(.ARM.exidx.text.i2c0_write_n_byte) refers to mpu6050.o(.text.i2c0_write_n_byte) for [Anonymous Symbol]
    mpu6050.o(.text.i2c0_read_n_byte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    mpu6050.o(.text.i2c0_read_n_byte) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for DL_I2C_flushControllerRXFIFO
    mpu6050.o(.text.i2c0_read_n_byte) refers to mpu6050.o(.bss.i2c0_read_n_byte.temp_reg_dddr) for [Anonymous Symbol]
    mpu6050.o(.ARM.exidx.text.i2c0_read_n_byte) refers to mpu6050.o(.text.i2c0_read_n_byte) for [Anonymous Symbol]
    mpu6050.o(.text.I2C_WriteReg) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    mpu6050.o(.text.I2C_WriteReg) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    mpu6050.o(.text.I2C_WriteReg) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    mpu6050.o(.ARM.exidx.text.I2C_WriteReg) refers to mpu6050.o(.text.I2C_WriteReg) for [Anonymous Symbol]
    mpu6050.o(.text.I2C_ReadReg) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    mpu6050.o(.text.I2C_ReadReg) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    mpu6050.o(.ARM.exidx.text.I2C_ReadReg) refers to mpu6050.o(.text.I2C_ReadReg) for [Anonymous Symbol]
    mpu6050.o(.text.Single_WriteI2C) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    mpu6050.o(.text.Single_WriteI2C) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    mpu6050.o(.ARM.exidx.text.Single_WriteI2C) refers to mpu6050.o(.text.Single_WriteI2C) for [Anonymous Symbol]
    mpu6050.o(.text.Single_ReadI2C) refers to mpu6050.o(.text.I2C_ReadReg) for I2C_ReadReg
    mpu6050.o(.ARM.exidx.text.Single_ReadI2C) refers to mpu6050.o(.text.Single_ReadI2C) for [Anonymous Symbol]
    mpu6050.o(.text.mpu6050_init) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    mpu6050.o(.text.mpu6050_init) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    mpu6050.o(.text.mpu6050_init) refers to mpu6050.o(.text.I2C_ReadReg) for I2C_ReadReg
    mpu6050.o(.text.mpu6050_init) refers to mpu6050.o(.bss.read_imu) for read_imu
    mpu6050.o(.ARM.exidx.text.mpu6050_init) refers to mpu6050.o(.text.mpu6050_init) for [Anonymous Symbol]
    mpu6050.o(.text.mpu6050_read) refers to mpu6050.o(.text.I2C_ReadReg) for I2C_ReadReg
    mpu6050.o(.text.mpu6050_read) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.mpu6050_read) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.text.mpu6050_read) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    mpu6050.o(.ARM.exidx.text.mpu6050_read) refers to mpu6050.o(.text.mpu6050_read) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.text.I2C_ReadReg) for I2C_ReadReg
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to dflti.o(.text) for __aeabi_i2d
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to ddiv.o(.text) for __aeabi_ddiv
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to f2d.o(.text) for __aeabi_f2d
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to daddsub.o(.text) for __aeabi_dadd
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to d2f.o(.text) for __aeabi_d2f
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to ffixi.o(.text) for __aeabi_f2iz
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.time) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.0) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.2) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.6) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.8) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.12) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.14) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.x) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.y) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.bss.MPU6050_ReadDatas_Proc.z) for [Anonymous Symbol]
    mpu6050.o(.ARM.exidx.text.MPU6050_ReadDatas_Proc) refers to mpu6050.o(.text.MPU6050_ReadDatas_Proc) for [Anonymous Symbol]
    mpu6050.o(.text.kalmanfiter) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    mpu6050.o(.text.kalmanfiter) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.text.kalmanfiter) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    mpu6050.o(.text.kalmanfiter) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    mpu6050.o(.ARM.exidx.text.kalmanfiter) refers to mpu6050.o(.text.kalmanfiter) for [Anonymous Symbol]
    mpu6050.o(.text.LPF_1st) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    mpu6050.o(.text.LPF_1st) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    mpu6050.o(.text.LPF_1st) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    mpu6050.o(.ARM.exidx.text.LPF_1st) refers to mpu6050.o(.text.LPF_1st) for [Anonymous Symbol]
    mpu6050.o(.text.AHRS_Geteuler) refers to mpu6050.o(.text.MPU6050_ReadDatas_Proc) for MPU6050_ReadDatas_Proc
    mpu6050.o(.text.AHRS_Geteuler) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    mpu6050.o(.text.AHRS_Geteuler) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    mpu6050.o(.text.AHRS_Geteuler) refers to sqrtf.o(i.sqrtf) for sqrtf
    mpu6050.o(.text.AHRS_Geteuler) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.text.AHRS_Geteuler) refers to f2d.o(.text) for __aeabi_f2d
    mpu6050.o(.text.AHRS_Geteuler) refers to atan.o(i.atan) for atan
    mpu6050.o(.text.AHRS_Geteuler) refers to dmul.o(.text) for __aeabi_dmul
    mpu6050.o(.text.AHRS_Geteuler) refers to daddsub.o(.text) for __aeabi_dadd
    mpu6050.o(.text.AHRS_Geteuler) refers to d2f.o(.text) for __aeabi_d2f
    mpu6050.o(.text.AHRS_Geteuler) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    mpu6050.o(.text.AHRS_Geteuler) refers to mpu6050.o(.bss.Gyro_Z_Measeure) for Gyro_Z_Measeure
    mpu6050.o(.text.AHRS_Geteuler) refers to mpu6050.o(.bss.pitch2) for pitch2
    mpu6050.o(.text.AHRS_Geteuler) refers to mpu6050.o(.bss.roll2) for roll2
    mpu6050.o(.text.AHRS_Geteuler) refers to mpu6050.o(.bss.Yaw) for Yaw
    mpu6050.o(.ARM.exidx.text.AHRS_Geteuler) refers to mpu6050.o(.text.AHRS_Geteuler) for [Anonymous Symbol]
    mpu6050.o(.text.IIR_I_Filter) refers to dmul.o(.text) for __aeabi_dmul
    mpu6050.o(.text.IIR_I_Filter) refers to daddsub.o(.text) for __aeabi_dadd
    mpu6050.o(.ARM.exidx.text.IIR_I_Filter) refers to mpu6050.o(.text.IIR_I_Filter) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__eqdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__eqdf2) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i.__gedf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__gedf2) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i.__gtdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__gtdf2) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i.__ledf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__ledf2) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i.__ltdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__ltdf2) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i.__nedf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__nedf2) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._deq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._deq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._dgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgeq) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgr) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dleq) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dls) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dneq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixui.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i.__eqsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__eqsf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i.__gesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gesf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__gtsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gtsf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__lesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__lesf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__ltsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__ltsf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__nesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__nesf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalbn.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to daddsub.o(.text) for __aeabi_dsub
    round.o(i.round) refers to dlef.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drlef.o(x$fpl$drleqf) for __aeabi_cdrcmple
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dgef.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgef.o(x$fpl$dgeqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dlef.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dlef.o(x$fpl$dleqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    drlef.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drlef.o(x$fpl$drleqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    drnd.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fsqrt.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalbn.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub.o(.text) for __aeabi_dadd
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(.text) for _btod_d2e
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    bigflt0.o(.text) refers to btod.o(.text) for _btod_emul
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(.text) refers to btod.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    ieee_status.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    btod_accurate_common.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (40 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (48 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.NVIC_EnableIRQ_Init), (44 bytes).
    Removing main.o(.ARM.exidx.text.NVIC_EnableIRQ_Init), (8 bytes).
    Removing main.o(.ARM.exidx.text.OLED_Show_Proc), (8 bytes).
    Removing main.o(.ARM.exidx.text.Protocol_Datas_Proc), (8 bytes).
    Removing main.o(.ARM.exidx.text.TIMA1_IRQHandler), (8 bytes).
    Removing main.o(.ARM.exidx.text.run_mode2), (8 bytes).
    Removing main.o(.ARM.exidx.text.run_mode3), (8 bytes).
    Removing main.o(.ARM.exidx.text.run_mode1), (8 bytes).
    Removing main.o(.ARM.exidx.text.run_mode4_v2), (8 bytes).
    Removing main.o(.bss.distance_timer), (1 bytes).
    Removing main.o(.bss.Angle_flag), (1 bytes).
    Removing main.o(.bss.straight_num), (1 bytes).
    Removing main.o(.bss.mode4_Turn_flag), (1 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing delay.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.text.uart0_send_char), (16 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing usart.o(.text.uart0_send_string), (32 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing usart.o(.text.usart0_send_byte), (16 bytes).
    Removing usart.o(.ARM.exidx.text.usart0_send_byte), (8 bytes).
    Removing usart.o(.text.usart0_send_bytes), (32 bytes).
    Removing usart.o(.ARM.exidx.text.usart0_send_bytes), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing usart.o(.text.fputc), (20 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing usart.o(.text.JustFloat_SendArray), (36 bytes).
    Removing usart.o(.ARM.exidx.text.JustFloat_SendArray), (8 bytes).
    Removing usart.o(.text.Float_to_Byte), (16 bytes).
    Removing usart.o(.ARM.exidx.text.Float_to_Byte), (8 bytes).
    Removing usart.o(.text.JustFloat_Test), (96 bytes).
    Removing usart.o(.ARM.exidx.text.JustFloat_Test), (8 bytes).
    Removing usart.o(.text.vofa_sendData), (128 bytes).
    Removing usart.o(.ARM.exidx.text.vofa_sendData), (8 bytes).
    Removing usart.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.ARM.exidx.text.Key_Read), (8 bytes).
    Removing key.o(.ARM.exidx.text.KEY_PROC), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_ColorTurn), (24 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ColorTurn), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing oled.o(.text.OLED_DisplayTurn), (38 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DisplayTurn), (8 bytes).
    Removing oled.o(.ARM.exidx.text.I2C_WaitAck), (8 bytes).
    Removing oled.o(.text.OLED_WriteData), (292 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteData), (8 bytes).
    Removing oled.o(.text.OLED_DisPlay_On), (30 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DisPlay_On), (8 bytes).
    Removing oled.o(.text.OLED_DisPlay_Off), (30 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DisPlay_Off), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Refresh), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.text.OLED_DrawPoint), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing oled.o(.text.OLED_DrawLine), (568 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawLine), (8 bytes).
    Removing oled.o(.text.OLED_DrawRectangle), (316 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawRectangle), (8 bytes).
    Removing oled.o(.text.OLED_DrawCircle), (904 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawCircle), (8 bytes).
    Removing oled.o(.text.OLED_DrawEllipse), (1368 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawEllipse), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_Pow), (18 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Pow), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowSignedNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowHexNum), (162 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHexNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowBinNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowFloatNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowChinese), (192 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChinese), (8 bytes).
    Removing oled.o(.text.OLED_ScrollDisplay), (152 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ScrollDisplay), (8 bytes).
    Removing oled.o(.text.OLED_ShowPicture), (272 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowPicture), (8 bytes).
    Removing oled.o(.text.OLED_Printf), (98 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Printf), (8 bytes).
    Removing oled.o(.text.OLED_GPIO_Init), (2 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_GPIO_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Update), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Show_String), (8 bytes).
    Removing oled.o(.text.OLED_DisplayLine), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DisplayLine), (8 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder.o(.text.Motor1_Get_Speed), (100 bytes).
    Removing encoder.o(.ARM.exidx.text.Motor1_Get_Speed), (8 bytes).
    Removing encoder.o(.text.Motor2_Get_Speed), (100 bytes).
    Removing encoder.o(.ARM.exidx.text.Motor2_Get_Speed), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.MEASURE_MOTORS_SPEED), (8 bytes).
    Removing motor_ctrl.o(.text), (0 bytes).
    Removing motor_ctrl.o(.text.PID_Limit), (40 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.PID_Limit), (8 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.PID_Calculate), (8 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.Set_PID_Param), (8 bytes).
    Removing motor_ctrl.o(.text.PWM_Limit), (20 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.PWM_Limit), (8 bytes).
    Removing motor_ctrl.o(.text.Set_Motor1_PWM), (44 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.Set_Motor1_PWM), (8 bytes).
    Removing motor_ctrl.o(.text.Set_Motor2_PWM), (44 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.Set_Motor2_PWM), (8 bytes).
    Removing motor_ctrl.o(.text.Set_Motor1_Speed), (112 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.Set_Motor1_Speed), (8 bytes).
    Removing motor_ctrl.o(.text.Set_Motor2_Speed), (116 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.Set_Motor2_Speed), (8 bytes).
    Removing motor_ctrl.o(.ARM.exidx.text.SET_MOTORS_SPEED), (8 bytes).
    Removing protocol.o(.text), (0 bytes).
    Removing protocol.o(.text.check_sum), (20 bytes).
    Removing protocol.o(.ARM.exidx.text.check_sum), (8 bytes).
    Removing protocol.o(.ARM.exidx.text.protocol_data_recv), (8 bytes).
    Removing protocol.o(.ARM.exidx.text.protocol_init), (8 bytes).
    Removing protocol.o(.ARM.exidx.text.receiving_process), (8 bytes).
    Removing protocol.o(.ARM.exidx.text.set_computer_value), (8 bytes).
    Removing gw_gray.o(.text), (0 bytes).
    Removing gw_gray.o(.ARM.exidx.text.Huidu_Read), (8 bytes).
    Removing gw_gray.o(.ARM.exidx.text.Huidu_Proc), (8 bytes).
    Removing mpu6050.o(.text), (0 bytes).
    Removing mpu6050.o(.text.i2c0_write_n_byte), (168 bytes).
    Removing mpu6050.o(.ARM.exidx.text.i2c0_write_n_byte), (8 bytes).
    Removing mpu6050.o(.text.i2c0_read_n_byte), (160 bytes).
    Removing mpu6050.o(.ARM.exidx.text.i2c0_read_n_byte), (8 bytes).
    Removing mpu6050.o(.text.I2C_WriteReg), (128 bytes).
    Removing mpu6050.o(.ARM.exidx.text.I2C_WriteReg), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.I2C_ReadReg), (8 bytes).
    Removing mpu6050.o(.text.Single_WriteI2C), (112 bytes).
    Removing mpu6050.o(.ARM.exidx.text.Single_WriteI2C), (8 bytes).
    Removing mpu6050.o(.text.Single_ReadI2C), (20 bytes).
    Removing mpu6050.o(.ARM.exidx.text.Single_ReadI2C), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.mpu6050_init), (8 bytes).
    Removing mpu6050.o(.text.mpu6050_read), (124 bytes).
    Removing mpu6050.o(.ARM.exidx.text.mpu6050_read), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_ReadDatas_Proc), (8 bytes).
    Removing mpu6050.o(.text.kalmanfiter), (88 bytes).
    Removing mpu6050.o(.ARM.exidx.text.kalmanfiter), (8 bytes).
    Removing mpu6050.o(.text.LPF_1st), (50 bytes).
    Removing mpu6050.o(.ARM.exidx.text.LPF_1st), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.AHRS_Geteuler), (8 bytes).
    Removing mpu6050.o(.text.IIR_I_Filter), (220 bytes).
    Removing mpu6050.o(.ARM.exidx.text.IIR_I_Filter), (8 bytes).
    Removing mpu6050.o(.bss.i2c0_write_n_byte.temp_reg_dddr), (1 bytes).
    Removing mpu6050.o(.bss.i2c0_read_n_byte.temp_reg_dddr), (1 bytes).
    Removing mpu6050.o(.data.b_IIR), (40 bytes).
    Removing mpu6050.o(.data.a_IIR), (40 bytes).
    Removing mpu6050.o(.bss.InPut_IIR), (120 bytes).
    Removing mpu6050.o(.bss.OutPut_IIR), (120 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (272 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (192 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (196 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).

307 unused section(s) (total 12138 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.c                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv6m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  dcmp.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/drnd.c                   0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/fsqrt.c                  0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/cfplib/ieee_status.c            0x00000000   Number         0  ieee_status.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  dscalbn.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dgeqf6m.s                       0x00000000   Number         0  dgef.o ABSOLUTE
    ../fplib/dleqf6m.s                       0x00000000   Number         0  dlef.o ABSOLUTE
    ../fplib/drleqf6m.s                      0x00000000   Number         0  drlef.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    Encoder.c                                0x00000000   Number         0  encoder.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    gw_gray.c                                0x00000000   Number         0  gw_gray.o ABSOLUTE
    key.c                                    0x00000000   Number         0  key.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor_ctrl.c                             0x00000000   Number         0  motor_ctrl.o ABSOLUTE
    mpu6050.c                                0x00000000   Number         0  mpu6050.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    protocol.c                               0x00000000   Number         0  protocol.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_null                           0x00000120   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000128   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x00000144   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000146   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000148   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000014a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000014c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000014c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000014c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000152   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000152   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000156   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000156   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000015e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000160   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000160   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000164   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0000016c   Section       56  rt_memcpy.o(.emb_text)
    .text                                    0x000001a4   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001d4   Section        0  rt_memcpy.o(.text)
    .text                                    0x00000258   Section      504  aeabi_sdivfast.o(.text)
    .text                                    0x00000450   Section        0  heapauxi.o(.text)
    .text                                    0x00000458   Section        0  d2f.o(.text)
    _dadd1                                   0x000004d5   Thumb Code   290  daddsub.o(.text)
    .text                                    0x000004d4   Section        0  daddsub.o(.text)
    _dsub1                                   0x000005f7   Thumb Code   470  daddsub.o(.text)
    .text                                    0x0000082c   Section        0  ddiv.o(.text)
    .text                                    0x00000c74   Section        0  dfixui.o(.text)
    .text                                    0x00000cbc   Section        0  dflti.o(.text)
    .text                                    0x00000d14   Section        0  dmul.o(.text)
    .text                                    0x00000f5c   Section        0  f2d.o(.text)
    .text                                    0x00000fb0   Section        0  fdiv.o(.text)
    .text                                    0x00001110   Section        0  ffixi.o(.text)
    .text                                    0x0000115c   Section        0  fflti.o(.text)
    .text                                    0x000011ba   Section        0  _rserrno.o(.text)
    .text                                    0x000011d0   Section        0  drnd.o(.text)
    .text                                    0x000012c0   Section        0  dscalbn.o(.text)
    .text                                    0x00001320   Section        0  fsqrt.o(.text)
    .text                                    0x000013b0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x000013b8   Section        0  dcmpin.o(.text)
    .text                                    0x00001458   Section        0  fcmpin.o(.text)
    .text                                    0x000014bc   Section        8  libspace.o(.text)
    .text                                    0x000014c4   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00001502   Section        0  exit.o(.text)
    .text                                    0x00001512   Section        0  cmpret.o(.text)
    .text                                    0x00001540   Section        0  dnan2.o(.text)
    .text                                    0x00001554   Section        0  fnan2.o(.text)
    .text                                    0x00001564   Section        0  retnan.o(.text)
    .text                                    0x000015c4   Section        0  sys_exit.o(.text)
    .text                                    0x000015d0   Section        2  use_no_semi.o(.text)
    .text                                    0x000015d2   Section        0  indicate_semi.o(.text)
    .text                                    0x000015d2   Section        0  __dczerorl2.o(.text)
    [Anonymous Symbol]                       0x00001628   Section        0  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_0                            0x00001814   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_1                            0x00001818   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_2                            0x0000181c   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_3                            0x00001820   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_4                            0x00001824   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_5                            0x00001828   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_6                            0x0000182c   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_7                            0x00001830   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_8                            0x00001834   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_9                            0x00001838   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_10                           0x0000183c   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_11                           0x00001840   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_12                           0x00001844   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_13                           0x00001848   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_14                           0x0000184c   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    __arm_cp.11_15                           0x00001850   Number         4  mpu6050.o(.text.AHRS_Geteuler)
    [Anonymous Symbol]                       0x00001854   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x0000185e   Section        0  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    [Anonymous Symbol]                       0x0000188c   Section        0  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    __arm_cp.3_0                             0x000018b0   Number         4  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    [Anonymous Symbol]                       0x000018b4   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    [Anonymous Symbol]                       0x000018dc   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    __arm_cp.39_0                            0x00001948   Number         4  dl_timer.o(.text.DL_TimerA_initPWMMode)
    [Anonymous Symbol]                       0x0000194c   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_1                            0x000019f8   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_2                            0x000019fc   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_3                            0x00001a00   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_4                            0x00001a04   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_6                            0x00001a08   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00001a0c   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00001aec   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x00001af0   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00001af4   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00001af8   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00001afc   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.26_0                            0x00001b14   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00001b18   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.13_0                            0x00001b2c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001b30   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00001b3c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001b40   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00001b58   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00001b5c   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00001b9c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001ba0   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00001ba4   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00001bb8   Section        0  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    __arm_cp.7_0                             0x00001bc8   Number         4  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    [Anonymous Symbol]                       0x00001bcc   Section        0  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_0                             0x00001c70   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_1                             0x00001c74   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_3                             0x00001c78   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_4                             0x00001c7c   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00001c80   Section        0  usart.o(.text.HAL_UART_Transmit)
    __arm_cp.4_0                             0x00001cac   Number         4  usart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x00001cb0   Section        0  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_0                             0x00001d90   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_1                             0x00001d94   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_2                             0x00001d98   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_3                             0x00001d9c   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_4                             0x00001da0   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_5                             0x00001da4   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_6                             0x00001da8   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_7                             0x00001dac   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_8                             0x00001db0   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_9                             0x00001db4   Number         4  gw_gray.o(.text.Huidu_Proc)
    __arm_cp.1_10                            0x00001db8   Number         4  gw_gray.o(.text.Huidu_Proc)
    [Anonymous Symbol]                       0x00001dbc   Section        0  gw_gray.o(.text.Huidu_Read)
    __arm_cp.0_0                             0x00001e08   Number         4  gw_gray.o(.text.Huidu_Read)
    [Anonymous Symbol]                       0x00001e0c   Section        0  mpu6050.o(.text.I2C_ReadReg)
    __arm_cp.3_0                             0x00001e90   Number         4  mpu6050.o(.text.I2C_ReadReg)
    __arm_cp.3_1                             0x00001e94   Number         4  mpu6050.o(.text.I2C_ReadReg)
    __arm_cp.3_2                             0x00001e98   Number         4  mpu6050.o(.text.I2C_ReadReg)
    __arm_cp.3_3                             0x00001e9c   Number         4  mpu6050.o(.text.I2C_ReadReg)
    __arm_cp.3_4                             0x00001ea0   Number         4  mpu6050.o(.text.I2C_ReadReg)
    I2C_WaitAck                              0x00001ea5   Thumb Code    76  oled.o(.text.I2C_WaitAck)
    [Anonymous Symbol]                       0x00001ea4   Section        0  oled.o(.text.I2C_WaitAck)
    __arm_cp.3_0                             0x00001ef0   Number         4  oled.o(.text.I2C_WaitAck)
    __arm_cp.3_1                             0x00001ef4   Number         4  oled.o(.text.I2C_WaitAck)
    __arm_cp.3_2                             0x00001ef8   Number         4  oled.o(.text.I2C_WaitAck)
    __arm_cp.3_3                             0x00001efc   Number         4  oled.o(.text.I2C_WaitAck)
    [Anonymous Symbol]                       0x00001f00   Section        0  key.o(.text.KEY_PROC)
    __arm_cp.1_1                             0x00001fd8   Number         4  key.o(.text.KEY_PROC)
    __arm_cp.1_2                             0x00001fdc   Number         4  key.o(.text.KEY_PROC)
    __arm_cp.1_3                             0x00001fe0   Number         4  key.o(.text.KEY_PROC)
    __arm_cp.1_4                             0x00001fe4   Number         4  key.o(.text.KEY_PROC)
    __arm_cp.1_5                             0x00001fe8   Number         4  key.o(.text.KEY_PROC)
    __arm_cp.1_6                             0x00001fec   Number         4  key.o(.text.KEY_PROC)
    __arm_cp.1_7                             0x00001ff0   Number         4  key.o(.text.KEY_PROC)
    [Anonymous Symbol]                       0x00001ff4   Section        0  key.o(.text.Key_Read)
    __arm_cp.0_0                             0x000020b4   Number         4  key.o(.text.Key_Read)
    __arm_cp.0_1                             0x000020b8   Number         4  key.o(.text.Key_Read)
    [Anonymous Symbol]                       0x000020bc   Section        0  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_0                             0x000021e4   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_1                             0x000021e8   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_2                             0x000021ec   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_3                             0x000021f0   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_4                             0x000021f4   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_5                             0x000021f8   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_6                             0x000021fc   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_7                             0x00002200   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_8                             0x00002204   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_9                             0x00002208   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_10                            0x0000220c   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_11                            0x00002210   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_12                            0x00002214   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_13                            0x00002218   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_14                            0x0000221c   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_15                            0x00002220   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_16                            0x00002224   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    __arm_cp.3_17                            0x00002228   Number         4  encoder.o(.text.MEASURE_MOTORS_SPEED)
    [Anonymous Symbol]                       0x0000222c   Section        0  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_0                             0x00002594   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_1                             0x00002598   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_2                             0x0000259c   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_3                             0x000025a0   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_4                             0x000025a4   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_5                             0x000025a8   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_6                             0x000025ac   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_7                             0x000025b0   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_8                             0x000025b4   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_9                             0x000025b8   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_10                            0x000025bc   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_11                            0x000025c0   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_12                            0x000025c4   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_13                            0x000025c8   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_14                            0x000025cc   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_15                            0x000025d0   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_16                            0x000025d4   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    __arm_cp.8_17                            0x000025d8   Number         4  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    [Anonymous Symbol]                       0x000025dc   Section        0  oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x00002600   Section        0  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00002740   Section        0  oled.o(.text.OLED_Refresh)
    __arm_cp.7_0                             0x000028ac   Number         4  oled.o(.text.OLED_Refresh)
    __arm_cp.7_1                             0x000028b0   Number         4  oled.o(.text.OLED_Refresh)
    [Anonymous Symbol]                       0x000028b4   Section        0  oled.o(.text.OLED_ShowBinNum)
    [Anonymous Symbol]                       0x00002920   Section        0  oled.o(.text.OLED_ShowChar)
    __arm_cp.14_0                            0x00002a0c   Number         4  oled.o(.text.OLED_ShowChar)
    __arm_cp.14_1                            0x00002a10   Number         4  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x00002a14   Section        0  oled.o(.text.OLED_ShowFloatNum)
    [Anonymous Symbol]                       0x00002be2   Section        0  oled.o(.text.OLED_ShowNum)
    [Anonymous Symbol]                       0x00002c60   Section        0  oled.o(.text.OLED_ShowSignedNum)
    [Anonymous Symbol]                       0x00002d0e   Section        0  oled.o(.text.OLED_ShowString)
    [Anonymous Symbol]                       0x00002d60   Section        0  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_38                            0x00002ed0   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_40                            0x00002edc   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_41                            0x00002ee0   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_42                            0x00002ee4   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_44                            0x00002ef4   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_49                            0x00002f10   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_53                            0x0000329c   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_55                            0x000032ac   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_57                            0x000032bc   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_59                            0x000032c8   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_62                            0x000032dc   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_9                             0x00003400   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_10                            0x00003404   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_11                            0x00003408   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_13                            0x00003418   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_14                            0x0000341c   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_15                            0x00003420   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_16                            0x00003424   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_19                            0x00003444   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_23                            0x00003454   Number         4  main.o(.text.OLED_Show_Proc)
    __arm_cp.2_28                            0x00003468   Number         4  main.o(.text.OLED_Show_Proc)
    [Anonymous Symbol]                       0x0000347c   Section        0  oled.o(.text.OLED_Show_String)
    __arm_cp.29_0                            0x00003570   Number         4  oled.o(.text.OLED_Show_String)
    __arm_cp.29_1                            0x00003574   Number         4  oled.o(.text.OLED_Show_String)
    [Anonymous Symbol]                       0x00003578   Section        0  oled.o(.text.OLED_Update)
    [Anonymous Symbol]                       0x00003580   Section        0  oled.o(.text.OLED_WR_Byte)
    __arm_cp.1_0                             0x000036b8   Number         4  oled.o(.text.OLED_WR_Byte)
    [Anonymous Symbol]                       0x000036bc   Section        0  motor_ctrl.o(.text.PID_Calculate)
    [Anonymous Symbol]                       0x00003778   Section        0  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_0                             0x00003868   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_1                             0x0000386c   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_2                             0x00003870   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_3                             0x00003874   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_4                             0x00003878   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_5                             0x0000387c   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_6                             0x00003880   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_7                             0x00003884   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_8                             0x00003888   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_9                             0x0000388c   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_10                            0x00003890   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_11                            0x00003894   Number         4  main.o(.text.Protocol_Datas_Proc)
    __arm_cp.3_12                            0x00003898   Number         4  main.o(.text.Protocol_Datas_Proc)
    [Anonymous Symbol]                       0x0000389c   Section        0  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    __arm_cp.8_0                             0x00003964   Number         4  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    __arm_cp.8_1                             0x00003968   Number         4  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    __arm_cp.8_3                             0x0000396c   Number         4  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    __arm_cp.8_4                             0x00003970   Number         4  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    __arm_cp.8_5                             0x00003974   Number         4  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    __arm_cp.8_6                             0x00003978   Number         4  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    [Anonymous Symbol]                       0x0000397c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00003a40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00003a44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00003a48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00003a4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00003a50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00003a54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00003a58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00003a5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x00003a60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x00003a64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_10                            0x00003a68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_11                            0x00003a6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_12                            0x00003a70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00003a74   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.6_0                             0x00003abc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.6_1                             0x00003ac0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.6_2                             0x00003ac4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00003ac8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_0                             0x00003b34   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_1                             0x00003b38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_2                             0x00003b3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_3                             0x00003b40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00003b44   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00003b58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00003b5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00003b60   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_0                             0x00003b98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_1                             0x00003b9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_2                             0x00003ba0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_3                             0x00003ba4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_4                             0x00003ba8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_5                             0x00003bac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00003bb0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_0                             0x00003c14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_1                             0x00003c18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_2                             0x00003c1c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_3                             0x00003c20   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_4                             0x00003c24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_5                             0x00003c28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_6                             0x00003c2c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00003c30   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00003c5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00003c60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00003c64   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00003c98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00003c9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00003ca0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00003ca4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00003ca8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00003cac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00003cb0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00003cb4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00003cb8   Section        0  motor_ctrl.o(.text.Set_PID_Param)
    [Anonymous Symbol]                       0x00003cc8   Section        0  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_0                             0x00003e24   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_1                             0x00003e28   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_2                             0x00003e2c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_3                             0x00003e30   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_4                             0x00003e34   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_5                             0x00003e38   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_6                             0x00003e3c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_7                             0x00003e40   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_8                             0x00003e44   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_9                             0x00003e48   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_10                            0x00003e4c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_11                            0x00003e50   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_12                            0x00003e54   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_13                            0x00003e58   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_14                            0x00003e5c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_15                            0x00003e60   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_16                            0x00003e64   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_17                            0x00003e68   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_18                            0x00003e6c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_19                            0x00003e70   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_20                            0x00003e74   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_21                            0x00003e78   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_22                            0x00003e7c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_23                            0x00003e80   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_24                            0x00003e84   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_25                            0x00003e88   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_26                            0x00003e8c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_27                            0x00003e90   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_28                            0x00003e94   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_29                            0x00003e98   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_30                            0x00003e9c   Number         4  main.o(.text.TIMA1_IRQHandler)
    __arm_cp.4_31                            0x00003ea0   Number         4  main.o(.text.TIMA1_IRQHandler)
    [Anonymous Symbol]                       0x00003ea4   Section        0  usart.o(.text.UART0_IRQHandler)
    __arm_cp.10_0                            0x00003ec8   Number         4  usart.o(.text.UART0_IRQHandler)
    __arm_cp.10_1                            0x00003ecc   Number         4  usart.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00003ed0   Section        0  delay.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00003ede   Section        0  delay.o(.text.delay_us)
    [Anonymous Symbol]                       0x00003ee8   Section        0  main.o(.text.main)
    __arm_cp.0_0                             0x00003f24   Number         4  main.o(.text.main)
    __arm_cp.0_1                             0x00003f28   Number         4  main.o(.text.main)
    __arm_cp.0_2                             0x00003f2c   Number         4  main.o(.text.main)
    [Anonymous Symbol]                       0x00003f30   Section        0  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_0                             0x000040e4   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_1                             0x000040e8   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_2                             0x000040ec   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_3                             0x000040f0   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_4                             0x000040f4   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_5                             0x000040f8   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_6                             0x000040fc   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_7                             0x00004100   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_8                             0x00004104   Number         4  mpu6050.o(.text.mpu6050_init)
    __arm_cp.6_9                             0x00004108   Number         4  mpu6050.o(.text.mpu6050_init)
    [Anonymous Symbol]                       0x0000410c   Section        0  protocol.o(.text.protocol_data_recv)
    [Anonymous Symbol]                       0x00004154   Section        0  protocol.o(.text.protocol_init)
    __arm_cp.2_1                             0x00004164   Number         4  protocol.o(.text.protocol_init)
    [Anonymous Symbol]                       0x00004168   Section        0  protocol.o(.text.receiving_process)
    __arm_cp.3_0                             0x00004498   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_1                             0x0000449c   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_2                             0x000044a0   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_3                             0x000044a4   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_4                             0x000044a8   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_5                             0x000044ac   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_7                             0x000044b0   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_8                             0x000044b4   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_10                            0x000044b8   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_11                            0x000044bc   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_12                            0x000044c0   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_13                            0x000044c4   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_14                            0x000044c8   Number         4  protocol.o(.text.receiving_process)
    __arm_cp.3_15                            0x000044cc   Number         4  protocol.o(.text.receiving_process)
    [Anonymous Symbol]                       0x000044d0   Section        0  main.o(.text.run_mode1)
    __arm_cp.7_0                             0x00004580   Number         4  main.o(.text.run_mode1)
    __arm_cp.7_1                             0x00004584   Number         4  main.o(.text.run_mode1)
    __arm_cp.7_2                             0x00004588   Number         4  main.o(.text.run_mode1)
    __arm_cp.7_13                            0x0000459c   Number         4  main.o(.text.run_mode1)
    __arm_cp.7_15                            0x000045a0   Number         4  main.o(.text.run_mode1)
    [Anonymous Symbol]                       0x000045a4   Section        0  main.o(.text.run_mode2)
    __arm_cp.5_0                             0x00004780   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_1                             0x00004784   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_2                             0x00004788   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_4                             0x0000479c   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_5                             0x000047a0   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_6                             0x000047a4   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_7                             0x000047a8   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_8                             0x000047ac   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_9                             0x000047b0   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_10                            0x000047b4   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_11                            0x000047b8   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_12                            0x000047bc   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_13                            0x000047c0   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_14                            0x000047c4   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_15                            0x000047c8   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_16                            0x000047cc   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_17                            0x000047d0   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_18                            0x000047d4   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_19                            0x000047d8   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_20                            0x000047dc   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_21                            0x000047e0   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_22                            0x000047e4   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_23                            0x000047e8   Number         4  main.o(.text.run_mode2)
    __arm_cp.5_24                            0x000047ec   Number         4  main.o(.text.run_mode2)
    [Anonymous Symbol]                       0x000047f8   Section        0  main.o(.text.run_mode3)
    __arm_cp.6_0                             0x00004a74   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_1                             0x00004a78   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_2                             0x00004a7c   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_4                             0x00004a90   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_5                             0x00004a94   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_6                             0x00004a98   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_7                             0x00004a9c   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_8                             0x00004aa0   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_9                             0x00004aa4   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_10                            0x00004aa8   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_11                            0x00004aac   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_12                            0x00004ab0   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_13                            0x00004ab4   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_14                            0x00004ab8   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_15                            0x00004abc   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_16                            0x00004ac0   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_17                            0x00004ac4   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_18                            0x00004ac8   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_20                            0x00004adc   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_21                            0x00004ae0   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_22                            0x00004ae4   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_23                            0x00004ae8   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_24                            0x00004aec   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_25                            0x00004af0   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_26                            0x00004af4   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_27                            0x00004af8   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_28                            0x00004afc   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_29                            0x00004b00   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_30                            0x00004b04   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_31                            0x00004b08   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_32                            0x00004b0c   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_33                            0x00004b10   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_34                            0x00004b14   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_35                            0x00004b18   Number         4  main.o(.text.run_mode3)
    __arm_cp.6_36                            0x00004b1c   Number         4  main.o(.text.run_mode3)
    [Anonymous Symbol]                       0x00004b20   Section        0  main.o(.text.run_mode4_v2)
    __arm_cp.8_0                             0x00004db4   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_1                             0x00004db8   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_2                             0x00004dbc   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_4                             0x00004dd4   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_5                             0x00004dd8   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_6                             0x00004ddc   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_7                             0x00004de0   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_8                             0x00004de4   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_9                             0x00004de8   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_10                            0x00004dec   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_11                            0x00004df0   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_12                            0x00004df4   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_13                            0x00004df8   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_14                            0x00004dfc   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_15                            0x00004e00   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_16                            0x00004e04   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_17                            0x00004e08   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_18                            0x00004e0c   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_20                            0x00004e18   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_21                            0x00004e1c   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_22                            0x00004e20   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_23                            0x00004e24   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_24                            0x00004e28   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_25                            0x00004e2c   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_26                            0x00004e30   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_27                            0x00004e34   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_28                            0x00004e38   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_29                            0x00004e3c   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_30                            0x00004e40   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_31                            0x00004e44   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_32                            0x00004e48   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_34                            0x00004e54   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_35                            0x00004e58   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_36                            0x00004e5c   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_37                            0x00004e60   Number         4  main.o(.text.run_mode4_v2)
    __arm_cp.8_38                            0x00004e64   Number         4  main.o(.text.run_mode4_v2)
    [Anonymous Symbol]                       0x00004e68   Section        0  protocol.o(.text.set_computer_value)
    __arm_cp.4_0                             0x00004edc   Number         4  protocol.o(.text.set_computer_value)
    __arm_cp.4_1                             0x00004ee0   Number         4  protocol.o(.text.set_computer_value)
    __arm_cp.4_2                             0x00004ee4   Number         4  protocol.o(.text.set_computer_value)
    .text_divfast                            0x00004ee8   Section      502  aeabi_sdivfast.o(.text_divfast)
    i.__ARM_fpclassify                       0x000050e0   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x0000510c   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x000051b8   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_underflow                0x000051c4   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._deq                                   0x000051d8   Section        0  dcmp.o(i._deq)
    i._dleq                                  0x000051ee   Section        0  dcmp.o(i._dleq)
    i._feq                                   0x00005208   Section        0  fcmp.o(i._feq)
    i._fgeq                                  0x0000521e   Section        0  fcmp.o(i._fgeq)
    i._fgr                                   0x00005234   Section        0  fcmp.o(i._fgr)
    i._fleq                                  0x0000524a   Section        0  fcmp.o(i._fleq)
    i._fls                                   0x00005264   Section        0  fcmp.o(i._fls)
    i.atan                                   0x0000527c   Section        0  atan.o(i.atan)
    i.round                                  0x00005498   Section        0  round.o(i.round)
    i.sqrtf                                  0x00005534   Section        0  sqrtf.o(i.sqrtf)
    x$fpl$deqf                               0x00005560   Section      100  deqf.o(x$fpl$deqf)
    x$fpl$dleqf                              0x000055c4   Section      100  dlef.o(x$fpl$dleqf)
    x$fpl$drleqf                             0x00005628   Section      104  drlef.o(x$fpl$drleqf)
    x$fpl$fadd                               0x00005690   Section      140  faddsub.o(x$fpl$fadd)
    _fadd1                                   0x0000569d   Thumb Code     0  faddsub.o(x$fpl$fadd)
    x$fpl$feqf                               0x0000571c   Section       84  feqf.o(x$fpl$feqf)
    x$fpl$fgeqf                              0x00005770   Section       84  fgef.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x000057c4   Section       84  flef.o(x$fpl$fleqf)
    x$fpl$fmul                               0x00005818   Section      176  fmul.o(x$fpl$fmul)
    x$fpl$fsub                               0x000058c8   Section      208  faddsub.o(x$fpl$fsub)
    _fsub1                                   0x000058d5   Thumb Code     0  faddsub.o(x$fpl$fsub)
    ddiv_reciptbl                            0x00005998   Data         128  ddiv.o(.constdata)
    .constdata                               0x00005998   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x00005998   Section        0  usenofp.o(x$fpl$usenofp)
    fdiv_tab                                 0x00005a18   Data          64  fdiv.o(.constdata)
    .constdata                               0x00005a18   Section       64  fdiv.o(.constdata)
    atanhi                                   0x00005a58   Data          32  atan.o(.constdata)
    .constdata                               0x00005a58   Section      152  atan.o(.constdata)
    atanlo                                   0x00005a78   Data          32  atan.o(.constdata)
    aTodd                                    0x00005a98   Data          40  atan.o(.constdata)
    aTeven                                   0x00005ac0   Data          48  atan.o(.constdata)
    gI2C_0ClockConfig                        0x000060e0   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x000060e0   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x000060e2   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x000060e2   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x000060e8   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x000060e8   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gTIMER_0ClockConfig                      0x000060f0   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x000060f0   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x000060f4   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x000060f4   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00006108   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00006108   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x0000610a   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x0000610a   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00006114   Section        0  main.o(.rodata.str1.1)
    MPU6050_ReadDatas_Proc.EKF.0             0x20200000   Data           4  mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.0)
    [Anonymous Symbol]                       0x20200000   Section        0  mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.0)
    MPU6050_ReadDatas_Proc.EKF.12            0x20200004   Data           4  mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.12)
    [Anonymous Symbol]                       0x20200004   Section        0  mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.12)
    MPU6050_ReadDatas_Proc.EKF.6             0x20200008   Data           4  mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.6)
    [Anonymous Symbol]                       0x20200008   Section        0  mpu6050.o(.data.MPU6050_ReadDatas_Proc.EKF.6)
    .bss                                     0x20200160   Section       96  libspace.o(.bss)
    Huidu_Proc.huidu_lasterror               0x202001d8   Data           4  gw_gray.o(.bss.Huidu_Proc.huidu_lasterror)
    [Anonymous Symbol]                       0x202001d8   Section        0  gw_gray.o(.bss.Huidu_Proc.huidu_lasterror)
    MPU6050_ReadDatas_Proc.EKF.14            0x20200200   Data           4  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.14)
    [Anonymous Symbol]                       0x20200200   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.14)
    MPU6050_ReadDatas_Proc.EKF.2             0x20200204   Data           4  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.2)
    [Anonymous Symbol]                       0x20200204   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.2)
    MPU6050_ReadDatas_Proc.EKF.8             0x20200208   Data           4  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.8)
    [Anonymous Symbol]                       0x20200208   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.EKF.8)
    MPU6050_ReadDatas_Proc.time              0x2020020c   Data           2  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.time)
    [Anonymous Symbol]                       0x2020020c   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.time)
    MPU6050_ReadDatas_Proc.x                 0x20200210   Data           4  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.x)
    [Anonymous Symbol]                       0x20200210   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.x)
    MPU6050_ReadDatas_Proc.y                 0x20200214   Data           4  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.y)
    [Anonymous Symbol]                       0x20200214   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.y)
    MPU6050_ReadDatas_Proc.z                 0x20200218   Data           4  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.z)
    [Anonymous Symbol]                       0x20200218   Section        0  mpu6050.o(.bss.MPU6050_ReadDatas_Proc.z)
    TIMA1_IRQHandler.count_100ms             0x202006c0   Data           2  main.o(.bss.TIMA1_IRQHandler.count_100ms)
    [Anonymous Symbol]                       0x202006c0   Section        0  main.o(.bss.TIMA1_IRQHandler.count_100ms)
    TIMA1_IRQHandler.count_10ms              0x202006c2   Data           2  main.o(.bss.TIMA1_IRQHandler.count_10ms)
    [Anonymous Symbol]                       0x202006c2   Section        0  main.o(.bss.TIMA1_IRQHandler.count_10ms)
    TIMA1_IRQHandler.t                       0x202006c4   Data           2  main.o(.bss.TIMA1_IRQHandler.t)
    [Anonymous Symbol]                       0x202006c4   Section        0  main.o(.bss.TIMA1_IRQHandler.t)
    parser                                   0x202008c0   Data          12  protocol.o(.bss.parser)
    [Anonymous Symbol]                       0x202008c0   Section        0  protocol.o(.bss.parser)
    recv_buf                                 0x202008d5   Data         128  protocol.o(.bss.recv_buf)
    [Anonymous Symbol]                       0x202008d5   Section        0  protocol.o(.bss.recv_buf)
    run_mode3.num                            0x2020095c   Data           1  main.o(.bss.run_mode3.num)
    [Anonymous Symbol]                       0x2020095c   Section        0  main.o(.bss.run_mode3.num)
    run_mode4_v2.num                         0x2020095d   Data           1  main.o(.bss.run_mode4_v2.num)
    [Anonymous Symbol]                       0x2020095d   Section        0  main.o(.bss.run_mode4_v2.num)
    set_computer_value.set_packet            0x20200960   Data          10  protocol.o(.bss.set_computer_value.set_packet)
    [Anonymous Symbol]                       0x20200960   Section        0  protocol.o(.bss.set_computer_value.set_packet)
    Heap_Mem                                 0x20200970   Data         512  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200970   Section      512  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200b70   Data        1024  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20200b70   Section     1024  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20200f70   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x00000121   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000129   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x00000145   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000149   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000014d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000014d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000014d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000153   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000153   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000157   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000157   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000015f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000161   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000161   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000165   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    __aeabi_memcpy4                          0x0000016d   Thumb Code    56  rt_memcpy.o(.emb_text)
    __aeabi_memcpy8                          0x0000016d   Thumb Code     0  rt_memcpy.o(.emb_text)
    Reset_Handler                            0x000001a5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000001a9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000001ab   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000001ad   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000001af   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000001b1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001b3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001b5   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memcpy                           0x000001d5   Thumb Code   130  rt_memcpy.o(.text)
    __rt_memcpy                              0x000001d5   Thumb Code     0  rt_memcpy.o(.text)
    __aeabi_uidivmod                         0x00000259   Thumb Code    28  aeabi_sdivfast.o(.text)
    __aeabi_idivmod                          0x00000275   Thumb Code   472  aeabi_sdivfast.o(.text)
    __use_two_region_memory                  0x00000451   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x00000453   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x00000455   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x00000459   Thumb Code     0  d2f.o(.text)
    __truncdfsf2                             0x00000459   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x00000459   Thumb Code   120  d2f.o(.text)
    __adddf3                                 0x000007cd   Thumb Code     0  daddsub.o(.text)
    __aeabi_dadd                             0x000007cd   Thumb Code     0  daddsub.o(.text)
    _dadd                                    0x000007cd   Thumb Code    26  daddsub.o(.text)
    __aeabi_dsub                             0x000007e7   Thumb Code     0  daddsub.o(.text)
    __subdf3                                 0x000007e7   Thumb Code     0  daddsub.o(.text)
    _dsub                                    0x000007e7   Thumb Code    22  daddsub.o(.text)
    __aeabi_drsub                            0x000007fd   Thumb Code     0  daddsub.o(.text)
    _drsb                                    0x000007fd   Thumb Code    28  daddsub.o(.text)
    __aeabi_ddiv                             0x0000082d   Thumb Code     0  ddiv.o(.text)
    __divdf3                                 0x0000082d   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x0000082d   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000c5d   Thumb Code    20  ddiv.o(.text)
    __aeabi_d2uiz                            0x00000c75   Thumb Code     0  dfixui.o(.text)
    _dfixu                                   0x00000c75   Thumb Code    68  dfixui.o(.text)
    __aeabi_i2d_normalise                    0x00000cbd   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x00000cff   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x00000cff   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x00000d0f   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x00000d0f   Thumb Code     0  dflti.o(.text)
    __aeabi_dmul                             0x00000d15   Thumb Code     0  dmul.o(.text)
    __muldf3                                 0x00000d15   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x00000d15   Thumb Code   558  dmul.o(.text)
    __aeabi_f2d                              0x00000f5d   Thumb Code     0  f2d.o(.text)
    __extendsfdf2                            0x00000f5d   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x00000f5d   Thumb Code    80  f2d.o(.text)
    __aeabi_fdiv                             0x00000fb1   Thumb Code     0  fdiv.o(.text)
    __divsf3                                 0x00000fb1   Thumb Code     0  fdiv.o(.text)
    _fdiv                                    0x00000fb1   Thumb Code   334  fdiv.o(.text)
    _frdiv                                   0x000010ff   Thumb Code     8  fdiv.o(.text)
    __aeabi_f2iz                             0x00001111   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x00001111   Thumb Code    76  ffixi.o(.text)
    __aeabi_i2f_normalise                    0x0000115d   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x000011a5   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x000011a5   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x000011b5   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x000011b5   Thumb Code     0  fflti.o(.text)
    __read_errno                             0x000011bb   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x000011c5   Thumb Code    12  _rserrno.o(.text)
    _drnd                                    0x000011d1   Thumb Code   232  drnd.o(.text)
    __ARM_scalbn                             0x000012c1   Thumb Code    84  dscalbn.o(.text)
    _fsqrt                                   0x00001321   Thumb Code   140  fsqrt.o(.text)
    __aeabi_errno_addr                       0x000013b1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x000013b1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x000013b1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __fpl_dcmp_InfNaN                        0x000013b9   Thumb Code   154  dcmpin.o(.text)
    __fpl_fcmp_InfNaN                        0x00001459   Thumb Code    96  fcmpin.o(.text)
    __user_libspace                          0x000014bd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x000014bd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x000014bd   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x000014c5   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x00001503   Thumb Code    16  exit.o(.text)
    __fpl_cmpreturn                          0x00001513   Thumb Code    46  cmpret.o(.text)
    __fpl_dcheck_NaN2                        0x00001541   Thumb Code    14  dnan2.o(.text)
    __fpl_fcheck_NaN2                        0x00001555   Thumb Code    10  fnan2.o(.text)
    __fpl_return_NaN                         0x00001565   Thumb Code    94  retnan.o(.text)
    _sys_exit                                0x000015c5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x000015d1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x000015d1   Thumb Code     2  use_no_semi.o(.text)
    __decompress                             0x000015d3   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x000015d3   Thumb Code    86  __dczerorl2.o(.text)
    __semihosting_library_function           0x000015d3   Thumb Code     0  indicate_semi.o(.text)
    AHRS_Geteuler                            0x00001629   Thumb Code   492  mpu6050.o(.text.AHRS_Geteuler)
    DL_Common_delayCycles                    0x00001855   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_I2C_fillControllerTXFIFO              0x0000185f   Thumb Code    44  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    DL_I2C_flushControllerTXFIFO             0x0000188d   Thumb Code    40  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_setClockConfig                    0x000018b5   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_TimerA_initPWMMode                    0x000018dd   Thumb Code   112  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x0000194d   Thumb Code   192  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00001a0d   Thumb Code   240  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001afd   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00001b19   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001b31   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001b41   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00001b5d   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001ba5   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataBlocking             0x00001bb9   Thumb Code    20  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    GROUP1_IRQHandler                        0x00001bcd   Thumb Code   164  encoder.o(.text.GROUP1_IRQHandler)
    HAL_UART_Transmit                        0x00001c81   Thumb Code    44  usart.o(.text.HAL_UART_Transmit)
    Huidu_Proc                               0x00001cb1   Thumb Code   224  gw_gray.o(.text.Huidu_Proc)
    Huidu_Read                               0x00001dbd   Thumb Code    76  gw_gray.o(.text.Huidu_Read)
    I2C_ReadReg                              0x00001e0d   Thumb Code   132  mpu6050.o(.text.I2C_ReadReg)
    KEY_PROC                                 0x00001f01   Thumb Code   216  key.o(.text.KEY_PROC)
    Key_Read                                 0x00001ff5   Thumb Code   192  key.o(.text.Key_Read)
    MEASURE_MOTORS_SPEED                     0x000020bd   Thumb Code   296  encoder.o(.text.MEASURE_MOTORS_SPEED)
    MPU6050_ReadDatas_Proc                   0x0000222d   Thumb Code   872  mpu6050.o(.text.MPU6050_ReadDatas_Proc)
    OLED_Clear                               0x000025dd   Thumb Code    36  oled.o(.text.OLED_Clear)
    OLED_Init                                0x00002601   Thumb Code   320  oled.o(.text.OLED_Init)
    OLED_Refresh                             0x00002741   Thumb Code   364  oled.o(.text.OLED_Refresh)
    OLED_ShowBinNum                          0x000028b5   Thumb Code   106  oled.o(.text.OLED_ShowBinNum)
    OLED_ShowChar                            0x00002921   Thumb Code   236  oled.o(.text.OLED_ShowChar)
    OLED_ShowFloatNum                        0x00002a15   Thumb Code   462  oled.o(.text.OLED_ShowFloatNum)
    OLED_ShowNum                             0x00002be3   Thumb Code   126  oled.o(.text.OLED_ShowNum)
    OLED_ShowSignedNum                       0x00002c61   Thumb Code   174  oled.o(.text.OLED_ShowSignedNum)
    OLED_ShowString                          0x00002d0f   Thumb Code    80  oled.o(.text.OLED_ShowString)
    OLED_Show_Proc                           0x00002d61   Thumb Code  1696  main.o(.text.OLED_Show_Proc)
    OLED_Show_String                         0x0000347d   Thumb Code   244  oled.o(.text.OLED_Show_String)
    OLED_Update                              0x00003579   Thumb Code     8  oled.o(.text.OLED_Update)
    OLED_WR_Byte                             0x00003581   Thumb Code   312  oled.o(.text.OLED_WR_Byte)
    PID_Calculate                            0x000036bd   Thumb Code   186  motor_ctrl.o(.text.PID_Calculate)
    Protocol_Datas_Proc                      0x00003779   Thumb Code   240  main.o(.text.Protocol_Datas_Proc)
    SET_MOTORS_SPEED                         0x0000389d   Thumb Code   200  motor_ctrl.o(.text.SET_MOTORS_SPEED)
    SYSCFG_DL_GPIO_init                      0x0000397d   Thumb Code   196  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00003a75   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x00003ac9   Thumb Code   108  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_SYSCTL_init                    0x00003b45   Thumb Code    20  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_TIMER_0_init                   0x00003b61   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00003bb1   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x00003c31   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00003c65   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Set_PID_Param                            0x00003cb9   Thumb Code    16  motor_ctrl.o(.text.Set_PID_Param)
    TIMA1_IRQHandler                         0x00003cc9   Thumb Code   348  main.o(.text.TIMA1_IRQHandler)
    UART0_IRQHandler                         0x00003ea5   Thumb Code    36  usart.o(.text.UART0_IRQHandler)
    delay_ms                                 0x00003ed1   Thumb Code    14  delay.o(.text.delay_ms)
    delay_us                                 0x00003edf   Thumb Code    10  delay.o(.text.delay_us)
    main                                     0x00003ee9   Thumb Code    60  main.o(.text.main)
    mpu6050_init                             0x00003f31   Thumb Code   436  mpu6050.o(.text.mpu6050_init)
    protocol_data_recv                       0x0000410d   Thumb Code    72  protocol.o(.text.protocol_data_recv)
    protocol_init                            0x00004155   Thumb Code    16  protocol.o(.text.protocol_init)
    receiving_process                        0x00004169   Thumb Code   816  protocol.o(.text.receiving_process)
    run_mode1                                0x000044d1   Thumb Code   176  main.o(.text.run_mode1)
    run_mode2                                0x000045a5   Thumb Code   476  main.o(.text.run_mode2)
    run_mode3                                0x000047f9   Thumb Code   636  main.o(.text.run_mode3)
    run_mode4_v2                             0x00004b21   Thumb Code   660  main.o(.text.run_mode4_v2)
    set_computer_value                       0x00004e69   Thumb Code   116  protocol.o(.text.set_computer_value)
    __aeabi_uidiv                            0x00004ee9   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x00004f2d   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    __ARM_fpclassify                         0x000050e1   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x0000510d   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x000051b9   Thumb Code    10  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_underflow                  0x000051c5   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    __aeabi_dcmpeq                           0x000051d9   Thumb Code     0  dcmp.o(i._deq)
    _deq                                     0x000051d9   Thumb Code    22  dcmp.o(i._deq)
    __aeabi_dcmple                           0x000051ef   Thumb Code     0  dcmp.o(i._dleq)
    _dleq                                    0x000051ef   Thumb Code    26  dcmp.o(i._dleq)
    __aeabi_fcmpeq                           0x00005209   Thumb Code     0  fcmp.o(i._feq)
    _feq                                     0x00005209   Thumb Code    22  fcmp.o(i._feq)
    __aeabi_fcmpge                           0x0000521f   Thumb Code     0  fcmp.o(i._fgeq)
    _fgeq                                    0x0000521f   Thumb Code    22  fcmp.o(i._fgeq)
    __aeabi_fcmpgt                           0x00005235   Thumb Code     0  fcmp.o(i._fgr)
    _fgr                                     0x00005235   Thumb Code    22  fcmp.o(i._fgr)
    __aeabi_fcmple                           0x0000524b   Thumb Code     0  fcmp.o(i._fleq)
    _fleq                                    0x0000524b   Thumb Code    26  fcmp.o(i._fleq)
    __aeabi_fcmplt                           0x00005265   Thumb Code     0  fcmp.o(i._fls)
    _fls                                     0x00005265   Thumb Code    22  fcmp.o(i._fls)
    atan                                     0x0000527d   Thumb Code   472  atan.o(i.atan)
    round                                    0x00005499   Thumb Code   142  round.o(i.round)
    sqrtf                                    0x00005535   Thumb Code    44  sqrtf.o(i.sqrtf)
    __aeabi_cdcmpeq                          0x00005561   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x00005561   Thumb Code    94  deqf.o(x$fpl$deqf)
    __aeabi_cdcmple                          0x000055c5   Thumb Code     0  dlef.o(x$fpl$dleqf)
    _dcmple                                  0x000055c5   Thumb Code    94  dlef.o(x$fpl$dleqf)
    __aeabi_cdrcmple                         0x00005629   Thumb Code     0  drlef.o(x$fpl$drleqf)
    _drcmple                                 0x00005629   Thumb Code   100  drlef.o(x$fpl$drleqf)
    __aeabi_fadd                             0x00005691   Thumb Code     0  faddsub.o(x$fpl$fadd)
    _fadd                                    0x00005691   Thumb Code   134  faddsub.o(x$fpl$fadd)
    __aeabi_cfcmpeq                          0x0000571d   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x0000571d   Thumb Code    78  feqf.o(x$fpl$feqf)
    _fcmpge                                  0x00005771   Thumb Code    78  fgef.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x000057c5   Thumb Code     0  flef.o(x$fpl$fleqf)
    _fcmple                                  0x000057c5   Thumb Code    78  flef.o(x$fpl$fleqf)
    __aeabi_fmul                             0x00005819   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x00005819   Thumb Code   172  fmul.o(x$fpl$fmul)
    __aeabi_fsub                             0x000058c9   Thumb Code     0  faddsub.o(x$fpl$fsub)
    _fsub                                    0x000058c9   Thumb Code   204  faddsub.o(x$fpl$fsub)
    __I$use$fp                               0x00005998   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F8x16                               0x00005af0   Data        1520  oled.o(.rodata.OLED_F8x16)
    Region$$Table$$Base                      0x00006140   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00006160   Number         0  anon$$obj.o(Region$$Table)
    OLED_View_Select                         0x2020000c   Data           1  main.o(.data.OLED_View_Select)
    Set_Motor_Param_Select                   0x2020000d   Data           1  protocol.o(.data.Set_Motor_Param_Select)
    Turn_PID_Flag                            0x2020000e   Data           1  motor_ctrl.o(.data.Turn_PID_Flag)
    mode                                     0x2020000f   Data           1  main.o(.data.mode)
    pid_Angle                                0x20200010   Data          56  motor_ctrl.o(.data.pid_Angle)
    pid_Distance                             0x20200048   Data          56  motor_ctrl.o(.data.pid_Distance)
    pid_Gyro                                 0x20200080   Data          56  motor_ctrl.o(.data.pid_Gyro)
    pid_Motor1_Speed                         0x202000b8   Data          56  motor_ctrl.o(.data.pid_Motor1_Speed)
    pid_Motor2_Speed                         0x202000f0   Data          56  motor_ctrl.o(.data.pid_Motor2_Speed)
    pid_Turn                                 0x20200128   Data          56  motor_ctrl.o(.data.pid_Turn)
    __libspace_start                         0x20200160   Data          96  libspace.o(.bss)
    Angle_PID_Flag                           0x202001c0   Data           1  motor_ctrl.o(.bss.Angle_PID_Flag)
    __temporary_stack_top$libspace           0x202001c0   Data           0  libspace.o(.bss)
    Basic_Speed                              0x202001c4   Data           4  main.o(.bss.Basic_Speed)
    Car_Mode                                 0x202001c8   Data           1  main.o(.bss.Car_Mode)
    Distance_PID_Flag                        0x202001c9   Data           1  motor_ctrl.o(.bss.Distance_PID_Flag)
    Gyro_PID_Flag                            0x202001ca   Data           1  motor_ctrl.o(.bss.Gyro_PID_Flag)
    Gyro_Z_Measeure                          0x202001cc   Data           4  mpu6050.o(.bss.Gyro_Z_Measeure)
    Huidu_Datas                              0x202001d0   Data           1  gw_gray.o(.bss.Huidu_Datas)
    Huidu_Error                              0x202001d4   Data           4  gw_gray.o(.bss.Huidu_Error)
    Huidu_Sum                                0x202001dc   Data           4  gw_gray.o(.bss.Huidu_Sum)
    Huidu_Target                             0x202001e0   Data           4  gw_gray.o(.bss.Huidu_Target)
    Key                                      0x202001e4   Data          24  key.o(.bss.Key)
    MOTOR1_ENABLE_FLAG                       0x202001fc   Data           1  motor_ctrl.o(.bss.MOTOR1_ENABLE_FLAG)
    MOTOR2_ENABLE_FLAG                       0x202001fd   Data           1  motor_ctrl.o(.bss.MOTOR2_ENABLE_FLAG)
    Measure_Distance                         0x2020021c   Data           4  encoder.o(.bss.Measure_Distance)
    Motor1_Encoder_Value                     0x20200220   Data           4  encoder.o(.bss.Motor1_Encoder_Value)
    Motor1_Lucheng                           0x20200224   Data           4  encoder.o(.bss.Motor1_Lucheng)
    Motor1_Speed                             0x20200228   Data           4  encoder.o(.bss.Motor1_Speed)
    Motor1_Target_Speed                      0x2020022c   Data           4  main.o(.bss.Motor1_Target_Speed)
    Motor2_Encoder_Value                     0x20200230   Data           4  encoder.o(.bss.Motor2_Encoder_Value)
    Motor2_Lucheng                           0x20200234   Data           4  encoder.o(.bss.Motor2_Lucheng)
    Motor2_Speed                             0x20200238   Data           4  encoder.o(.bss.Motor2_Speed)
    Motor2_Target_Speed                      0x2020023c   Data           4  main.o(.bss.Motor2_Target_Speed)
    OLED_GRAM                                0x20200240   Data        1152  oled.o(.bss.OLED_GRAM)
    Target_Angle                             0x202006c8   Data           4  main.o(.bss.Target_Angle)
    Target_ChaSu                             0x202006cc   Data           4  main.o(.bss.Target_ChaSu)
    Target_Distance                          0x202006d0   Data           4  main.o(.bss.Target_Distance)
    Target_Gyro                              0x202006d4   Data           4  main.o(.bss.Target_Gyro)
    Yaw                                      0x202006d8   Data           4  mpu6050.o(.bss.Yaw)
    flag_1s                                  0x202006dc   Data           1  main.o(.bss.flag_1s)
    gPWM_0Backup                             0x202006e0   Data         188  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gTIMER_0Backup                           0x2020079c   Data         188  ti_msp_dl_config.o(.bss.gTIMER_0Backup)
    key_val                                  0x20200858   Data           1  main.o(.bss.key_val)
    mpu6050                                  0x2020085c   Data         100  mpu6050.o(.bss.mpu6050)
    pitch2                                   0x202008cc   Data           4  mpu6050.o(.bss.pitch2)
    read_imu                                 0x202008d0   Data           5  mpu6050.o(.bss.read_imu)
    roll2                                    0x20200958   Data           4  mpu6050.o(.bss.roll2)
    semicycle_num                            0x2020095e   Data           1  main.o(.bss.semicycle_num)
    start_flag                               0x2020096a   Data           1  main.o(.bss.start_flag)
    stop_flag                                0x2020096b   Data           1  main.o(.bss.stop_flag)
    total_distant_cnt                        0x2020096c   Data           4  main.o(.bss.total_distant_cnt)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000062c0, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x000061a8])

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00006160, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           93    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          580  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1059    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x00000002   Code   RO         1060    !!handler_null      c_p.l(__scatter.o)
    0x00000122   0x00000122   0x00000006   PAD
    0x00000128   0x00000128   0x0000001c   Code   RO         1063    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000144   0x00000144   0x00000002   Code   RO          902    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000146   0x00000146   0x00000000   Code   RO          928    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          930    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          932    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          935    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          937    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          939    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          942    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          944    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          946    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          948    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          950    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          952    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          954    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          956    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          958    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          960    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          962    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          966    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          968    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          970    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          972    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000002   Code   RO          973    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000148   0x00000148   0x00000002   Code   RO         1012    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1040    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1042    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1045    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1048    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1050    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1053    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000002   Code   RO         1054    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO          671    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO          817    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000014c   0x0000014c   0x00000006   Code   RO          829    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000152   0x00000152   0x00000000   Code   RO          819    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000152   0x00000152   0x00000004   Code   RO          820    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000156   0x00000156   0x00000000   Code   RO          822    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000156   0x00000156   0x00000008   Code   RO          823    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000015e   0x0000015e   0x00000002   Code   RO          912    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000160   0x00000160   0x00000000   Code   RO          981    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000160   0x00000160   0x00000004   Code   RO          982    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000164   0x00000164   0x00000006   Code   RO          983    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000016a   0x0000016a   0x00000002   PAD
    0x0000016c   0x0000016c   0x00000038   Code   RO          566    .emb_text           c_p.l(rt_memcpy.o)
    0x000001a4   0x000001a4   0x00000030   Code   RO           94    .text               startup_mspm0g350x_uvision.o
    0x000001d4   0x000001d4   0x00000082   Code   RO          567    .text               c_p.l(rt_memcpy.o)
    0x00000256   0x00000256   0x00000002   PAD
    0x00000258   0x00000258   0x000001f8   Code   RO          570    .text               c_p.l(aeabi_sdivfast.o)
    0x00000450   0x00000450   0x00000006   Code   RO          578    .text               c_p.l(heapauxi.o)
    0x00000456   0x00000456   0x00000002   PAD
    0x00000458   0x00000458   0x0000007c   Code   RO          582    .text               fz_ps.l(d2f.o)
    0x000004d4   0x000004d4   0x00000358   Code   RO          584    .text               fz_ps.l(daddsub.o)
    0x0000082c   0x0000082c   0x00000448   Code   RO          610    .text               fz_ps.l(ddiv.o)
    0x00000c74   0x00000c74   0x00000048   Code   RO          613    .text               fz_ps.l(dfixui.o)
    0x00000cbc   0x00000cbc   0x00000058   Code   RO          615    .text               fz_ps.l(dflti.o)
    0x00000d14   0x00000d14   0x00000248   Code   RO          617    .text               fz_ps.l(dmul.o)
    0x00000f5c   0x00000f5c   0x00000054   Code   RO          619    .text               fz_ps.l(f2d.o)
    0x00000fb0   0x00000fb0   0x00000160   Code   RO          651    .text               fz_ps.l(fdiv.o)
    0x00001110   0x00001110   0x0000004c   Code   RO          654    .text               fz_ps.l(ffixi.o)
    0x0000115c   0x0000115c   0x0000005e   Code   RO          656    .text               fz_ps.l(fflti.o)
    0x000011ba   0x000011ba   0x00000016   Code   RO          676    .text               c_p.l(_rserrno.o)
    0x000011d0   0x000011d0   0x000000f0   Code   RO          777    .text               fz_ps.l(drnd.o)
    0x000012c0   0x000012c0   0x00000060   Code   RO          779    .text               fz_ps.l(dscalbn.o)
    0x00001320   0x00001320   0x00000090   Code   RO          787    .text               fz_ps.l(fsqrt.o)
    0x000013b0   0x000013b0   0x00000008   Code   RO          836    .text               c_p.l(rt_errno_addr_intlibspace.o)
    0x000013b8   0x000013b8   0x000000a0   Code   RO          860    .text               fz_ps.l(dcmpin.o)
    0x00001458   0x00001458   0x00000064   Code   RO          862    .text               fz_ps.l(fcmpin.o)
    0x000014bc   0x000014bc   0x00000008   Code   RO          864    .text               c_p.l(libspace.o)
    0x000014c4   0x000014c4   0x0000003e   Code   RO          867    .text               c_p.l(sys_stackheap_outer.o)
    0x00001502   0x00001502   0x00000010   Code   RO          891    .text               c_p.l(exit.o)
    0x00001512   0x00001512   0x0000002e   Code   RO          903    .text               fz_ps.l(cmpret.o)
    0x00001540   0x00001540   0x00000014   Code   RO          905    .text               fz_ps.l(dnan2.o)
    0x00001554   0x00001554   0x00000010   Code   RO          907    .text               fz_ps.l(fnan2.o)
    0x00001564   0x00001564   0x0000005e   Code   RO          974    .text               fz_ps.l(retnan.o)
    0x000015c2   0x000015c2   0x00000002   PAD
    0x000015c4   0x000015c4   0x0000000c   Code   RO          976    .text               c_p.l(sys_exit.o)
    0x000015d0   0x000015d0   0x00000002   Code   RO         1001    .text               c_p.l(use_no_semi.o)
    0x000015d2   0x000015d2   0x00000000   Code   RO         1003    .text               c_p.l(indicate_semi.o)
    0x000015d2   0x000015d2   0x00000056   Code   RO         1057    .text               c_p.l(__dczerorl2.o)
    0x00001628   0x00001628   0x0000022c   Code   RO          349    .text.AHRS_Geteuler  mpu6050.o
    0x00001854   0x00001854   0x0000000a   Code   RO          383    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x0000185e   0x0000185e   0x0000002c   Code   RO          396    .text.DL_I2C_fillControllerTXFIFO  driverlib.a(dl_i2c.o)
    0x0000188a   0x0000188a   0x00000002   PAD
    0x0000188c   0x0000188c   0x00000028   Code   RO          398    .text.DL_I2C_flushControllerTXFIFO  driverlib.a(dl_i2c.o)
    0x000018b4   0x000018b4   0x00000026   Code   RO          392    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x000018da   0x000018da   0x00000002   PAD
    0x000018dc   0x000018dc   0x00000070   Code   RO          502    .text.DL_TimerA_initPWMMode  driverlib.a(dl_timer.o)
    0x0000194c   0x0000194c   0x000000c0   Code   RO          446    .text.DL_Timer_initPWMMode  driverlib.a(dl_timer.o)
    0x00001a0c   0x00001a0c   0x000000f0   Code   RO          428    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x00001afc   0x00001afc   0x0000001c   Code   RO          476    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00001b18   0x00001b18   0x00000018   Code   RO          450    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00001b30   0x00001b30   0x00000010   Code   RO          430    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00001b40   0x00001b40   0x0000001c   Code   RO          424    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00001b5c   0x00001b5c   0x00000048   Code   RO          523    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001ba4   0x00001ba4   0x00000012   Code   RO          525    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00001bb6   0x00001bb6   0x00000002   PAD
    0x00001bb8   0x00001bb8   0x00000014   Code   RO          537    .text.DL_UART_transmitDataBlocking  driverlib.a(dl_uart.o)
    0x00001bcc   0x00001bcc   0x000000b4   Code   RO          227    .text.GROUP1_IRQHandler  encoder.o
    0x00001c80   0x00001c80   0x00000030   Code   RO          121    .text.HAL_UART_Transmit  usart.o
    0x00001cb0   0x00001cb0   0x0000010c   Code   RO          312    .text.Huidu_Proc    gw_gray.o
    0x00001dbc   0x00001dbc   0x00000050   Code   RO          310    .text.Huidu_Read    gw_gray.o
    0x00001e0c   0x00001e0c   0x00000098   Code   RO          333    .text.I2C_ReadReg   mpu6050.o
    0x00001ea4   0x00001ea4   0x0000005c   Code   RO          161    .text.I2C_WaitAck   oled.o
    0x00001f00   0x00001f00   0x000000f4   Code   RO          145    .text.KEY_PROC      key.o
    0x00001ff4   0x00001ff4   0x000000c8   Code   RO          143    .text.Key_Read      key.o
    0x000020bc   0x000020bc   0x00000170   Code   RO          233    .text.MEASURE_MOTORS_SPEED  encoder.o
    0x0000222c   0x0000222c   0x000003b0   Code   RO          343    .text.MPU6050_ReadDatas_Proc  mpu6050.o
    0x000025dc   0x000025dc   0x00000024   Code   RO          171    .text.OLED_Clear    oled.o
    0x00002600   0x00002600   0x00000140   Code   RO          209    .text.OLED_Init     oled.o
    0x00002740   0x00002740   0x00000174   Code   RO          169    .text.OLED_Refresh  oled.o
    0x000028b4   0x000028b4   0x0000006a   Code   RO          195    .text.OLED_ShowBinNum  oled.o
    0x0000291e   0x0000291e   0x00000002   PAD
    0x00002920   0x00002920   0x000000f4   Code   RO          183    .text.OLED_ShowChar  oled.o
    0x00002a14   0x00002a14   0x000001ce   Code   RO          197    .text.OLED_ShowFloatNum  oled.o
    0x00002be2   0x00002be2   0x0000007e   Code   RO          189    .text.OLED_ShowNum  oled.o
    0x00002c60   0x00002c60   0x000000ae   Code   RO          191    .text.OLED_ShowSignedNum  oled.o
    0x00002d0e   0x00002d0e   0x00000050   Code   RO          185    .text.OLED_ShowString  oled.o
    0x00002d5e   0x00002d5e   0x00000002   PAD
    0x00002d60   0x00002d60   0x0000071c   Code   RO           43    .text.OLED_Show_Proc  main.o
    0x0000347c   0x0000347c   0x000000fc   Code   RO          213    .text.OLED_Show_String  oled.o
    0x00003578   0x00003578   0x00000008   Code   RO          211    .text.OLED_Update   oled.o
    0x00003580   0x00003580   0x0000013c   Code   RO          157    .text.OLED_WR_Byte  oled.o
    0x000036bc   0x000036bc   0x000000ba   Code   RO          252    .text.PID_Calculate  motor_ctrl.o
    0x00003776   0x00003776   0x00000002   PAD
    0x00003778   0x00003778   0x00000124   Code   RO           45    .text.Protocol_Datas_Proc  main.o
    0x0000389c   0x0000389c   0x000000e0   Code   RO          266    .text.SET_MOTORS_SPEED  motor_ctrl.o
    0x0000397c   0x0000397c   0x000000f8   Code   RO            6    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00003a74   0x00003a74   0x00000054   Code   RO           14    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00003ac8   0x00003ac8   0x0000007c   Code   RO           10    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00003b44   0x00003b44   0x0000001c   Code   RO            8    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00003b60   0x00003b60   0x00000050   Code   RO           12    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00003bb0   0x00003bb0   0x00000080   Code   RO           16    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00003c30   0x00003c30   0x00000034   Code   RO            2    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00003c64   0x00003c64   0x00000054   Code   RO            4    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00003cb8   0x00003cb8   0x00000010   Code   RO          254    .text.Set_PID_Param  motor_ctrl.o
    0x00003cc8   0x00003cc8   0x000001dc   Code   RO           47    .text.TIMA1_IRQHandler  main.o
    0x00003ea4   0x00003ea4   0x0000002c   Code   RO          133    .text.UART0_IRQHandler  usart.o
    0x00003ed0   0x00003ed0   0x0000000e   Code   RO          101    .text.delay_ms      delay.o
    0x00003ede   0x00003ede   0x0000000a   Code   RO          103    .text.delay_us      delay.o
    0x00003ee8   0x00003ee8   0x00000048   Code   RO           39    .text.main          main.o
    0x00003f30   0x00003f30   0x000001dc   Code   RO          339    .text.mpu6050_init  mpu6050.o
    0x0000410c   0x0000410c   0x00000048   Code   RO          290    .text.protocol_data_recv  protocol.o
    0x00004154   0x00004154   0x00000014   Code   RO          292    .text.protocol_init  protocol.o
    0x00004168   0x00004168   0x00000368   Code   RO          294    .text.receiving_process  protocol.o
    0x000044d0   0x000044d0   0x000000d4   Code   RO           53    .text.run_mode1     main.o
    0x000045a4   0x000045a4   0x00000254   Code   RO           49    .text.run_mode2     main.o
    0x000047f8   0x000047f8   0x00000328   Code   RO           51    .text.run_mode3     main.o
    0x00004b20   0x00004b20   0x00000348   Code   RO           55    .text.run_mode4_v2  main.o
    0x00004e68   0x00004e68   0x00000080   Code   RO          296    .text.set_computer_value  protocol.o
    0x00004ee8   0x00004ee8   0x000001f6   Code   RO          571    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x000050de   0x000050de   0x00000002   PAD
    0x000050e0   0x000050e0   0x0000002c   Code   RO          812    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x0000510c   0x0000510c   0x000000ac   Code   RO          814    i.__kernel_poly     m_ps.l(poly.o)
    0x000051b8   0x000051b8   0x0000000a   Code   RO          799    i.__mathlib_dbl_infnan  m_ps.l(dunder.o)
    0x000051c2   0x000051c2   0x00000002   PAD
    0x000051c4   0x000051c4   0x00000014   Code   RO          804    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x000051d8   0x000051d8   0x00000016   Code   RO          592    i._deq              fz_ps.l(dcmp.o)
    0x000051ee   0x000051ee   0x0000001a   Code   RO          595    i._dleq             fz_ps.l(dcmp.o)
    0x00005208   0x00005208   0x00000016   Code   RO          633    i._feq              fz_ps.l(fcmp.o)
    0x0000521e   0x0000521e   0x00000016   Code   RO          634    i._fgeq             fz_ps.l(fcmp.o)
    0x00005234   0x00005234   0x00000016   Code   RO          635    i._fgr              fz_ps.l(fcmp.o)
    0x0000524a   0x0000524a   0x0000001a   Code   RO          636    i._fleq             fz_ps.l(fcmp.o)
    0x00005264   0x00005264   0x00000016   Code   RO          637    i._fls              fz_ps.l(fcmp.o)
    0x0000527a   0x0000527a   0x00000002   PAD
    0x0000527c   0x0000527c   0x0000021c   Code   RO          661    i.atan              m_ps.l(atan.o)
    0x00005498   0x00005498   0x0000009c   Code   RO          665    i.round             m_ps.l(round.o)
    0x00005534   0x00005534   0x0000002c   Code   RO          668    i.sqrtf             m_ps.l(sqrtf.o)
    0x00005560   0x00005560   0x00000064   Code   RO          769    x$fpl$deqf          fz_ps.l(deqf.o)
    0x000055c4   0x000055c4   0x00000064   Code   RO          773    x$fpl$dleqf         fz_ps.l(dlef.o)
    0x00005628   0x00005628   0x00000068   Code   RO          775    x$fpl$drleqf        fz_ps.l(drlef.o)
    0x00005690   0x00005690   0x0000008c   Code   RO          621    x$fpl$fadd          fz_ps.l(faddsub.o)
    0x0000571c   0x0000571c   0x00000054   Code   RO          781    x$fpl$feqf          fz_ps.l(feqf.o)
    0x00005770   0x00005770   0x00000054   Code   RO          783    x$fpl$fgeqf         fz_ps.l(fgef.o)
    0x000057c4   0x000057c4   0x00000054   Code   RO          785    x$fpl$fleqf         fz_ps.l(flef.o)
    0x00005818   0x00005818   0x000000b0   Code   RO          658    x$fpl$fmul          fz_ps.l(fmul.o)
    0x000058c8   0x000058c8   0x000000d0   Code   RO          623    x$fpl$fsub          fz_ps.l(faddsub.o)
    0x00005998   0x00005998   0x00000000   Code   RO          797    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x00005998   0x00005998   0x00000080   Data   RO          611    .constdata          fz_ps.l(ddiv.o)
    0x00005a18   0x00005a18   0x00000040   Data   RO          652    .constdata          fz_ps.l(fdiv.o)
    0x00005a58   0x00005a58   0x00000098   Data   RO          662    .constdata          m_ps.l(atan.o)
    0x00005af0   0x00005af0   0x000005f0   Data   RO          217    .rodata.OLED_F8x16  oled.o
    0x000060e0   0x000060e0   0x00000002   Data   RO           28    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x000060e2   0x000060e2   0x00000003   Data   RO           24    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x000060e5   0x000060e5   0x00000003   PAD
    0x000060e8   0x000060e8   0x00000008   Data   RO           25    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x000060f0   0x000060f0   0x00000003   Data   RO           26    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x000060f3   0x000060f3   0x00000001   PAD
    0x000060f4   0x000060f4   0x00000014   Data   RO           27    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00006108   0x00006108   0x00000002   Data   RO           29    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x0000610a   0x0000610a   0x0000000a   Data   RO           30    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00006114   0x00006114   0x00000026   Data   RO           71    .rodata.str1.1      main.o
    0x0000613a   0x0000613a   0x00000006   PAD
    0x00006140   0x00006140   0x00000020   Data   RO         1056    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00006160, Size: 0x00000f70, Max: 0x00008000, ABSOLUTE, COMPRESSED[0x00000048])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   COMPRESSED   0x00000004   Data   RW          362    .data.MPU6050_ReadDatas_Proc.EKF.0  mpu6050.o
    0x20200004   COMPRESSED   0x00000004   Data   RW          366    .data.MPU6050_ReadDatas_Proc.EKF.12  mpu6050.o
    0x20200008   COMPRESSED   0x00000004   Data   RW          364    .data.MPU6050_ReadDatas_Proc.EKF.6  mpu6050.o
    0x2020000c   COMPRESSED   0x00000001   Data   RW           59    .data.OLED_View_Select  main.o
    0x2020000d   COMPRESSED   0x00000001   Data   RW          300    .data.Set_Motor_Param_Select  protocol.o
    0x2020000e   COMPRESSED   0x00000001   Data   RW          270    .data.Turn_PID_Flag  motor_ctrl.o
    0x2020000f   COMPRESSED   0x00000001   Data   RW           63    .data.mode          main.o
    0x20200010   COMPRESSED   0x00000038   Data   RW          279    .data.pid_Angle     motor_ctrl.o
    0x20200048   COMPRESSED   0x00000038   Data   RW          277    .data.pid_Distance  motor_ctrl.o
    0x20200080   COMPRESSED   0x00000038   Data   RW          278    .data.pid_Gyro      motor_ctrl.o
    0x202000b8   COMPRESSED   0x00000038   Data   RW          274    .data.pid_Motor1_Speed  motor_ctrl.o
    0x202000f0   COMPRESSED   0x00000038   Data   RW          275    .data.pid_Motor2_Speed  motor_ctrl.o
    0x20200128   COMPRESSED   0x00000038   Data   RW          276    .data.pid_Turn      motor_ctrl.o
    0x20200160        -       0x00000060   Zero   RW          865    .bss                c_p.l(libspace.o)
    0x202001c0        -       0x00000001   Zero   RW          273    .bss.Angle_PID_Flag  motor_ctrl.o
    0x202001c1   COMPRESSED   0x00000003   PAD
    0x202001c4        -       0x00000004   Zero   RW           58    .bss.Basic_Speed    main.o
    0x202001c8        -       0x00000001   Zero   RW           57    .bss.Car_Mode       main.o
    0x202001c9        -       0x00000001   Zero   RW          271    .bss.Distance_PID_Flag  motor_ctrl.o
    0x202001ca        -       0x00000001   Zero   RW          272    .bss.Gyro_PID_Flag  motor_ctrl.o
    0x202001cb   COMPRESSED   0x00000001   PAD
    0x202001cc        -       0x00000004   Zero   RW          371    .bss.Gyro_Z_Measeure  mpu6050.o
    0x202001d0        -       0x00000001   Zero   RW          318    .bss.Huidu_Datas    gw_gray.o
    0x202001d1   COMPRESSED   0x00000003   PAD
    0x202001d4        -       0x00000004   Zero   RW          317    .bss.Huidu_Error    gw_gray.o
    0x202001d8        -       0x00000004   Zero   RW          315    .bss.Huidu_Proc.huidu_lasterror  gw_gray.o
    0x202001dc        -       0x00000004   Zero   RW          316    .bss.Huidu_Sum      gw_gray.o
    0x202001e0        -       0x00000004   Zero   RW          314    .bss.Huidu_Target   gw_gray.o
    0x202001e4        -       0x00000018   Zero   RW          147    .bss.Key            key.o
    0x202001fc        -       0x00000001   Zero   RW          268    .bss.MOTOR1_ENABLE_FLAG  motor_ctrl.o
    0x202001fd        -       0x00000001   Zero   RW          269    .bss.MOTOR2_ENABLE_FLAG  motor_ctrl.o
    0x202001fe   COMPRESSED   0x00000002   PAD
    0x20200200        -       0x00000004   Zero   RW          367    .bss.MPU6050_ReadDatas_Proc.EKF.14  mpu6050.o
    0x20200204        -       0x00000004   Zero   RW          363    .bss.MPU6050_ReadDatas_Proc.EKF.2  mpu6050.o
    0x20200208        -       0x00000004   Zero   RW          365    .bss.MPU6050_ReadDatas_Proc.EKF.8  mpu6050.o
    0x2020020c        -       0x00000002   Zero   RW          360    .bss.MPU6050_ReadDatas_Proc.time  mpu6050.o
    0x2020020e   COMPRESSED   0x00000002   PAD
    0x20200210        -       0x00000004   Zero   RW          368    .bss.MPU6050_ReadDatas_Proc.x  mpu6050.o
    0x20200214        -       0x00000004   Zero   RW          369    .bss.MPU6050_ReadDatas_Proc.y  mpu6050.o
    0x20200218        -       0x00000004   Zero   RW          370    .bss.MPU6050_ReadDatas_Proc.z  mpu6050.o
    0x2020021c        -       0x00000004   Zero   RW          239    .bss.Measure_Distance  encoder.o
    0x20200220        -       0x00000004   Zero   RW          235    .bss.Motor1_Encoder_Value  encoder.o
    0x20200224        -       0x00000004   Zero   RW          240    .bss.Motor1_Lucheng  encoder.o
    0x20200228        -       0x00000004   Zero   RW          237    .bss.Motor1_Speed   encoder.o
    0x2020022c        -       0x00000004   Zero   RW           68    .bss.Motor1_Target_Speed  main.o
    0x20200230        -       0x00000004   Zero   RW          236    .bss.Motor2_Encoder_Value  encoder.o
    0x20200234        -       0x00000004   Zero   RW          241    .bss.Motor2_Lucheng  encoder.o
    0x20200238        -       0x00000004   Zero   RW          238    .bss.Motor2_Speed   encoder.o
    0x2020023c        -       0x00000004   Zero   RW           69    .bss.Motor2_Target_Speed  main.o
    0x20200240        -       0x00000480   Zero   RW          218    .bss.OLED_GRAM      oled.o
    0x202006c0        -       0x00000002   Zero   RW           65    .bss.TIMA1_IRQHandler.count_100ms  main.o
    0x202006c2        -       0x00000002   Zero   RW           64    .bss.TIMA1_IRQHandler.count_10ms  main.o
    0x202006c4        -       0x00000002   Zero   RW           66    .bss.TIMA1_IRQHandler.t  main.o
    0x202006c6   COMPRESSED   0x00000002   PAD
    0x202006c8        -       0x00000004   Zero   RW           62    .bss.Target_Angle   main.o
    0x202006cc        -       0x00000004   Zero   RW           67    .bss.Target_ChaSu   main.o
    0x202006d0        -       0x00000004   Zero   RW           60    .bss.Target_Distance  main.o
    0x202006d4        -       0x00000004   Zero   RW           61    .bss.Target_Gyro    main.o
    0x202006d8        -       0x00000004   Zero   RW          374    .bss.Yaw            mpu6050.o
    0x202006dc        -       0x00000001   Zero   RW           70    .bss.flag_1s        main.o
    0x202006dd   COMPRESSED   0x00000003   PAD
    0x202006e0        -       0x000000bc   Zero   RW           22    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x2020079c        -       0x000000bc   Zero   RW           23    .bss.gTIMER_0Backup  ti_msp_dl_config.o
    0x20200858        -       0x00000001   Zero   RW           72    .bss.key_val        main.o
    0x20200859   COMPRESSED   0x00000003   PAD
    0x2020085c        -       0x00000064   Zero   RW          361    .bss.mpu6050        mpu6050.o
    0x202008c0        -       0x0000000c   Zero   RW          298    .bss.parser         protocol.o
    0x202008cc        -       0x00000004   Zero   RW          372    .bss.pitch2         mpu6050.o
    0x202008d0        -       0x00000005   Zero   RW          355    .bss.read_imu       mpu6050.o
    0x202008d5        -       0x00000080   Zero   RW          299    .bss.recv_buf       protocol.o
    0x20200955   COMPRESSED   0x00000003   PAD
    0x20200958        -       0x00000004   Zero   RW          373    .bss.roll2          mpu6050.o
    0x2020095c        -       0x00000001   Zero   RW           77    .bss.run_mode3.num  main.o
    0x2020095d        -       0x00000001   Zero   RW           78    .bss.run_mode4_v2.num  main.o
    0x2020095e        -       0x00000001   Zero   RW           75    .bss.semicycle_num  main.o
    0x2020095f   COMPRESSED   0x00000001   PAD
    0x20200960        -       0x0000000a   Zero   RW          301    .bss.set_computer_value.set_packet  protocol.o
    0x2020096a        -       0x00000001   Zero   RW           74    .bss.start_flag     main.o
    0x2020096b        -       0x00000001   Zero   RW           73    .bss.stop_flag      main.o
    0x2020096c        -       0x00000004   Zero   RW           76    .bss.total_distant_cnt  main.o
    0x20200970        -       0x00000200   Zero   RW           92    HEAP                startup_mspm0g350x_uvision.o
    0x20200b70        -       0x00000400   Zero   RW           91    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        24          0          0          0          0        703   delay.o
       548         88          0          0         28       6556   encoder.o
       348        144          0          0         17       4417   gw_gray.o
       444         36          0          0         24       4760   key.o
      5116       1006         38          2         46      14898   main.o
       426         24          0        337          5      10444   motor_ctrl.o
      2128        196          0         12        147      18072   mpu6050.o
      2588         44       1520          0       1152      45876   oled.o
      1092         90          0          1        150       7023   protocol.o
        48         22        192          0       1536        680   startup_mspm0g350x_uvision.o
       828        180         48          0        376      33658   ti_msp_dl_config.o
        92         12          0          0          0       7289   usart.o

    ----------------------------------------------------------------------
     13688       <USER>       <GROUP>        352       3504     154376   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0         10          0         23          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0         92   _rserrno.o
      1006          4          0          0          0        184   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
       186          0          0          0          0        144   rt_memcpy.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        802   dl_common.o
       122          4          0          0          0       8589   dl_i2c.o
       640        196          0          0          0      40195   dl_timer.o
       110         12          0          0          0      14127   dl_uart.o
        46          0          0          0          0         60   cmpret.o
       124          4          0          0          0         72   d2f.o
       856         20          0          0          0        208   daddsub.o
        48          0          0          0          0        136   dcmp.o
       160          6          0          0          0         76   dcmpin.o
      1096         26        128          0          0        112   ddiv.o
       100          4          0          0          0         92   deqf.o
        72          4          0          0          0         68   dfixui.o
        88          0          0          0          0         92   dflti.o
       100          4          0          0          0         92   dlef.o
       584         26          0          0          0         84   dmul.o
        20          6          0          0          0         68   dnan2.o
       104          4          0          0          0        104   drlef.o
       240          8          0          0          0         76   drnd.o
        96         12          0          0          0         72   dscalbn.o
        84          4          0          0          0         60   f2d.o
       348          8          0          0          0        160   faddsub.o
       114          0          0          0          0        340   fcmp.o
       100          4          0          0          0         68   fcmpin.o
       352         10         64          0          0         92   fdiv.o
        84          4          0          0          0         76   feqf.o
        76          0          0          0          0         68   ffixi.o
        94          0          0          0          0         92   fflti.o
        84          4          0          0          0         76   fgef.o
        84          4          0          0          0         76   flef.o
       176          4          0          0          0         80   fmul.o
        16          6          0          0          0         68   fnan2.o
       144          4          0          0          0         72   fsqrt.o
        94          0          0          0          0         68   retnan.o
         0          0          0          0          0          0   usenofp.o
       540         68        152          0          0        112   atan.o
        30          6          0          0          0        136   dunder.o
        44          4          0          0          0         60   fpclassify.o
       172          0          0          0          0         76   poly.o
       156         14          0          0          0         84   round.o
        44          0          0          0          0         72   sqrtf.o

    ----------------------------------------------------------------------
      9056        <USER>        <GROUP>          0         96      68097   Library Totals
        30          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1574         26          0          0         96       1036   c_p.l
       882        212          0          0          0      63713   driverlib.a
      5584        176        192          0          0       2808   fz_ps.l
       986         92        152          0          0        540   m_ps.l

    ----------------------------------------------------------------------
      9056        <USER>        <GROUP>          0         96      68097   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     22744       2352       2184        352       3600     219957   Grand Totals
     22744       2352       2184         72       3600     219957   ELF Image Totals (compressed)
     22744       2352       2184         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                24928 (  24.34kB)
    Total RW  Size (RW Data + ZI Data)              3952 (   3.86kB)
    Total ROM Size (Code + RO Data + RW Data)      25000 (  24.41kB)

==============================================================================

