Dependencies for Project 'M0_G3507', Target 'M0_G3507': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)()
F (..\MCU_Drivers\ti_msp_dl_config.c)(0x66A787C0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
F (..\MCU_Drivers\M0_G3507.syscfg)(0x66A787C0)()
F (..\MCU_Drivers\driverlib.a)(0x662FCA6A)()
F (..\User\main.c)(0x6889D31E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MMD)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\Hardware\key.h)(0x66A8EFCC)
I (..\User\delay.h)(0x669DB1FD)
I (..\Hardware\oled.h)(0x6690CB6D)
I (..\Hardware\bmp.h)(0x668E4F75)
I (..\User\usart.h)(0x668FE4B4)
I (..\Hardware\Encoder.h)(0x6889D31E)
I (..\Hardware\motor_ctrl.h)(0x669F302F)
I (..\Hardware\protocol.h)(0x6690E6FA)
I (..\Hardware\gw_gray.h)(0x66A7BD0C)
I (..\Hardware\mpu6050\mpu6050.h)(0x66991979)
F (.\startup_mspm0g350x_uvision.s)(0x66991236)(--cpu Cortex-M0+ -g --diag_suppress=A1950W

--pd "__UVISION_VERSION SETA 540"

--pd "__MSPM0G3507__ SETA 1"

--list .\listings\startup_mspm0g350x_uvision.lst

--xref -o .\objects\startup_mspm0g350x_uvision.o

--depend .\objects\startup_mspm0g350x_uvision.d)
F (..\User\delay.c)(0x669DB4EC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/delay.o -MMD)
I (..\User\delay.h)(0x669DB1FD)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
F (..\User\usart.c)(0x669DB448)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/usart.o -MMD)
I (..\User\usart.h)(0x668FE4B4)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\Hardware\protocol.h)(0x6690E6FA)
F (..\Hardware\key.c)(0x66A8EFCC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MMD)
I (..\Hardware\key.h)(0x66A8EFCC)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\User\delay.h)(0x669DB1FD)
I (..\Hardware\oled.h)(0x6690CB6D)
I (..\Hardware\motor_ctrl.h)(0x669F302F)
F (..\Hardware\oled.c)(0x669DB4E9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled.o -MMD)
I (..\Hardware\oled.h)(0x6690CB6D)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\Hardware\oledfont.h)(0x668E5166)
I (..\User\delay.h)(0x669DB1FD)
F (..\Hardware\Encoder.c)(0x66A6FBB0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/encoder.o -MMD)
I (..\Hardware\Encoder.h)(0x6889D31E)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\Hardware\mpu6050\mpu6050.h)(0x66991979)
I (..\Hardware\key.h)(0x66A8EFCC)
F (..\Hardware\motor_ctrl.c)(0x66A4B807)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor_ctrl.o -MMD)
I (..\Hardware\motor_ctrl.h)(0x669F302F)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
F (..\Hardware\protocol.c)(0x669F5B41)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/protocol.o -MMD)
I (..\Hardware\protocol.h)(0x6690E6FA)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\Hardware\motor_ctrl.h)(0x669F302F)
I (..\User\usart.h)(0x668FE4B4)
F (..\Hardware\gw_gray.c)(0x66A7BD0C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/gw_gray.o -MMD)
I (..\Hardware\gw_gray.h)(0x66A7BD0C)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\User\delay.h)(0x669DB1FD)
F (..\Hardware\mpu6050\mpu6050.c)(0x669DB705)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ./Include -I ../MCU_Drivers -I ../User -I ../Hardware -I ../Hardware/mpu6050

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/mpu6050.o -MMD)
I (..\Hardware\mpu6050\mpu6050.h)(0x66991979)
I (..\User\main.h)(0x66AA946A)
I (..\MCU_Drivers\ti_msp_dl_config.h)(0x66A787C0)
I (..\MCU_Drivers\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (Include\core_cm0plus.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\MCU_Drivers\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\MCU_Drivers\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\MCU_Drivers\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\User\delay.h)(0x669DB1FD)
