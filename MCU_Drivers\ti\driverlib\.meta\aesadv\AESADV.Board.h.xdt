%%{
/*
 * Copyright (c) 2018 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== AES.Board.h.xdt ========
 */

    /* args[] passed by /ti/driverlib/templates/Board.h.xdt */
    let AESADV = args[0];
    let content = args[1];

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let stat = AESADV.$static;

    switch(content) {
        case "Define":
            printDefine();
            break;
        case "Declare":
            printDeclare();
            break;
        default:
            /* do nothing */
            break;
    }
%%}
%
% function printDefine() {
/* Defines for AESADV */
#define AES_BLOCK_SIZE                                                     (16)
%   let keyStr = "#define AES_KEY_SIZE";
%   let keySize = (stat.keySize == "DL_AESADV_KEY_SIZE_128_BIT") ? 16 : 32;
%   let keyStr2 = "(" + keySize + ")";
`keyStr.padEnd(40, " ") + keyStr2.padStart(39, " ")`

%
%   if (stat.initApplicationIV && stat.iv.length) {
/*
 * This IV must be initialized in the application code prior to calling
 * SYSCFG_DL_AESADV_init.
 */
extern uint8_t `stat.iv`[AES_BLOCK_SIZE];
%   }
%
%   if (stat.initApplicationNonce && stat.nonce.length) {
/*
 * This nonce must be initialized in the application code prior to calling
 * SYSCFG_DL_AESADV_init.
 */
extern uint8_t `stat.nonce`[AES_BLOCK_SIZE];
%   }
% }
%
% function printDeclare() {
void SYSCFG_DL_AESADV_init(void);
% }
