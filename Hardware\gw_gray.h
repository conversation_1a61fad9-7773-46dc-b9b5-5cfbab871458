#ifndef __GW_GRAY_H__
#define __GW_GRAY_H__

#include "main.h"

#define Read_Huidu_IO1	 ((DL_GPIO_readPins(Huidu_IN1_PORT, Huidu_IN1_PIN)==Huidu_IN1_PIN)?1:0)
#define Read_Huidu_IO2	 ((DL_GPIO_readPins(Huidu_IN2_PORT, Huidu_IN2_PIN)==Huidu_IN2_PIN)?1:0)
#define Read_Huidu_IO3	 ((DL_GPIO_readPins(Huidu_IN3_PORT, Huidu_IN3_PIN)==Huidu_IN3_PIN)?1:0)

#define Read_Huidu_IO4	 ((DL_GPIO_readPins(Huidu_IN4_PORT, Huidu_IN4_PIN)==Huidu_IN4_PIN)?1:0)

#define Read_Huidu_IO5	 ((DL_GPIO_readPins(Huidu_IN5_PORT, Huidu_IN5_PIN)==Huidu_IN5_PIN)?1:0)
#define Read_Huidu_IO6	 ((DL_GPIO_readPins(Huidu_IN6_PORT, Huidu_IN6_PIN)==Huidu_IN6_PIN)?1:0)
#define Read_Huidu_IO7	 ((DL_GPIO_readPins(Huidu_IN7_PORT, Huidu_IN7_PIN)==Huidu_IN7_PIN)?1:0)

uint8_t Huidu_Read(void);
float Huidu_Proc(uint8_t huidu_data);




extern float Huidu_Target;
extern uint8_t Huidu_Datas;
extern float Huidu_Error;
extern int Huidu_Sum;

#endif 
