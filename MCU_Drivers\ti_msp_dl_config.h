/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_0 */
#define PWM_0_INST                                                         TIMA0
#define PWM_0_INST_IRQHandler                                   TIMA0_IRQHandler
#define PWM_0_INST_INT_IRQN                                     (TIMA0_INT_IRQn)
#define PWM_0_INST_CLK_FREQ                                             16000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_0_C0_PORT                                                 GPIOB
#define GPIO_PWM_0_C0_PIN                                         DL_GPIO_PIN_14
#define GPIO_PWM_0_C0_IOMUX                                      (IOMUX_PINCM31)
#define GPIO_PWM_0_C0_IOMUX_FUNC                     IOMUX_PINCM31_PF_TIMA0_CCP0
#define GPIO_PWM_0_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_0_C1_PORT                                                 GPIOA
#define GPIO_PWM_0_C1_PIN                                          DL_GPIO_PIN_7
#define GPIO_PWM_0_C1_IOMUX                                      (IOMUX_PINCM14)
#define GPIO_PWM_0_C1_IOMUX_FUNC                     IOMUX_PINCM14_PF_TIMA0_CCP1
#define GPIO_PWM_0_C1_IDX                                    DL_TIMER_CC_1_INDEX



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMA1)
#define TIMER_0_INST_IRQHandler                                 TIMA1_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMA1_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                            (15U)




/* Defines for I2C_0 */
#define I2C_0_INST                                                          I2C1
#define I2C_0_INST_IRQHandler                                    I2C1_IRQHandler
#define I2C_0_INST_INT_IRQN                                        I2C1_INT_IRQn
#define I2C_0_BUS_SPEED_HZ                                                100000
#define GPIO_I2C_0_SDA_PORT                                                GPIOA
#define GPIO_I2C_0_SDA_PIN                                        DL_GPIO_PIN_30
#define GPIO_I2C_0_IOMUX_SDA                                      (IOMUX_PINCM5)
#define GPIO_I2C_0_IOMUX_SDA_FUNC                       IOMUX_PINCM5_PF_I2C1_SDA
#define GPIO_I2C_0_SCL_PORT                                                GPIOA
#define GPIO_I2C_0_SCL_PIN                                        DL_GPIO_PIN_29
#define GPIO_I2C_0_IOMUX_SCL                                      (IOMUX_PINCM4)
#define GPIO_I2C_0_IOMUX_SCL_FUNC                       IOMUX_PINCM4_PF_I2C1_SCL


/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_32_MHZ_9600_BAUD                                       (208)
#define UART_0_FBRD_32_MHZ_9600_BAUD                                        (21)





/* Port definition for Pin Group BEEP */
#define BEEP_PORT                                                        (GPIOB)

/* Defines for PIN_0: GPIOB.13 with pinCMx 30 on package pin 1 */
#define BEEP_PIN_0_PIN                                          (DL_GPIO_PIN_13)
#define BEEP_PIN_0_IOMUX                                         (IOMUX_PINCM30)
/* Defines for LED1: GPIOB.19 with pinCMx 45 on package pin 16 */
#define LED_LED1_PORT                                                    (GPIOB)
#define LED_LED1_PIN                                            (DL_GPIO_PIN_19)
#define LED_LED1_IOMUX                                           (IOMUX_PINCM45)
/* Defines for LED2: GPIOA.26 with pinCMx 59 on package pin 30 */
#define LED_LED2_PORT                                                    (GPIOA)
#define LED_LED2_PIN                                            (DL_GPIO_PIN_26)
#define LED_LED2_IOMUX                                           (IOMUX_PINCM59)
/* Defines for LED3: GPIOA.27 with pinCMx 60 on package pin 31 */
#define LED_LED3_PORT                                                    (GPIOA)
#define LED_LED3_PIN                                            (DL_GPIO_PIN_27)
#define LED_LED3_IOMUX                                           (IOMUX_PINCM60)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for KEY1: GPIOB.25 with pinCMx 56 on package pin 27 */
#define KEY_KEY1_PIN                                            (DL_GPIO_PIN_25)
#define KEY_KEY1_IOMUX                                           (IOMUX_PINCM56)
/* Defines for KEY2: GPIOB.26 with pinCMx 57 on package pin 28 */
#define KEY_KEY2_PIN                                            (DL_GPIO_PIN_26)
#define KEY_KEY2_IOMUX                                           (IOMUX_PINCM57)
/* Defines for KEY3: GPIOB.27 with pinCMx 58 on package pin 29 */
#define KEY_KEY3_PIN                                            (DL_GPIO_PIN_27)
#define KEY_KEY3_IOMUX                                           (IOMUX_PINCM58)
/* Port definition for Pin Group OLED */
#define OLED_PORT                                                        (GPIOA)

/* Defines for SCL: GPIOA.1 with pinCMx 2 on package pin 34 */
#define OLED_SCL_PIN                                             (DL_GPIO_PIN_1)
#define OLED_SCL_IOMUX                                            (IOMUX_PINCM2)
/* Defines for SDA: GPIOA.0 with pinCMx 1 on package pin 33 */
#define OLED_SDA_PIN                                             (DL_GPIO_PIN_0)
#define OLED_SDA_IOMUX                                            (IOMUX_PINCM1)
/* Port definition for Pin Group Encoder */
#define Encoder_PORT                                                     (GPIOB)

/* Defines for A: GPIOB.11 with pinCMx 28 on package pin 63 */
// pins affected by this interrupt request:["A","B","C","D"]
#define Encoder_INT_IRQN                                        (GPIOB_INT_IRQn)
#define Encoder_INT_IIDX                        (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define Encoder_A_IIDX                                      (DL_GPIO_IIDX_DIO11)
#define Encoder_A_PIN                                           (DL_GPIO_PIN_11)
#define Encoder_A_IOMUX                                          (IOMUX_PINCM28)
/* Defines for B: GPIOB.12 with pinCMx 29 on package pin 64 */
#define Encoder_B_IIDX                                      (DL_GPIO_IIDX_DIO12)
#define Encoder_B_PIN                                           (DL_GPIO_PIN_12)
#define Encoder_B_IOMUX                                          (IOMUX_PINCM29)
/* Defines for C: GPIOB.4 with pinCMx 17 on package pin 52 */
#define Encoder_C_IIDX                                       (DL_GPIO_IIDX_DIO4)
#define Encoder_C_PIN                                            (DL_GPIO_PIN_4)
#define Encoder_C_IOMUX                                          (IOMUX_PINCM17)
/* Defines for D: GPIOB.5 with pinCMx 18 on package pin 53 */
#define Encoder_D_IIDX                                       (DL_GPIO_IIDX_DIO5)
#define Encoder_D_PIN                                            (DL_GPIO_PIN_5)
#define Encoder_D_IOMUX                                          (IOMUX_PINCM18)
/* Port definition for Pin Group Motor_Ctrl */
#define Motor_Ctrl_PORT                                                  (GPIOB)

/* Defines for AIN1: GPIOB.9 with pinCMx 26 on package pin 61 */
#define Motor_Ctrl_AIN1_PIN                                      (DL_GPIO_PIN_9)
#define Motor_Ctrl_AIN1_IOMUX                                    (IOMUX_PINCM26)
/* Defines for AIN2: GPIOB.10 with pinCMx 27 on package pin 62 */
#define Motor_Ctrl_AIN2_PIN                                     (DL_GPIO_PIN_10)
#define Motor_Ctrl_AIN2_IOMUX                                    (IOMUX_PINCM27)
/* Defines for BIN1: GPIOB.6 with pinCMx 23 on package pin 58 */
#define Motor_Ctrl_BIN1_PIN                                      (DL_GPIO_PIN_6)
#define Motor_Ctrl_BIN1_IOMUX                                    (IOMUX_PINCM23)
/* Defines for BIN2: GPIOB.7 with pinCMx 24 on package pin 59 */
#define Motor_Ctrl_BIN2_PIN                                      (DL_GPIO_PIN_7)
#define Motor_Ctrl_BIN2_IOMUX                                    (IOMUX_PINCM24)
/* Defines for IN2: GPIOB.16 with pinCMx 33 on package pin 4 */
#define Huidu_IN2_PORT                                                   (GPIOB)
#define Huidu_IN2_PIN                                           (DL_GPIO_PIN_16)
#define Huidu_IN2_IOMUX                                          (IOMUX_PINCM33)
/* Defines for IN1: GPIOB.15 with pinCMx 32 on package pin 3 */
#define Huidu_IN1_PORT                                                   (GPIOB)
#define Huidu_IN1_PIN                                           (DL_GPIO_PIN_15)
#define Huidu_IN1_IOMUX                                          (IOMUX_PINCM32)
/* Defines for IN3: GPIOA.12 with pinCMx 34 on package pin 5 */
#define Huidu_IN3_PORT                                                   (GPIOA)
#define Huidu_IN3_PIN                                           (DL_GPIO_PIN_12)
#define Huidu_IN3_IOMUX                                          (IOMUX_PINCM34)
/* Defines for IN4: GPIOA.13 with pinCMx 35 on package pin 6 */
#define Huidu_IN4_PORT                                                   (GPIOA)
#define Huidu_IN4_PIN                                           (DL_GPIO_PIN_13)
#define Huidu_IN4_IOMUX                                          (IOMUX_PINCM35)
/* Defines for IN5: GPIOA.14 with pinCMx 36 on package pin 7 */
#define Huidu_IN5_PORT                                                   (GPIOA)
#define Huidu_IN5_PIN                                           (DL_GPIO_PIN_14)
#define Huidu_IN5_IOMUX                                          (IOMUX_PINCM36)
/* Defines for IN6: GPIOA.15 with pinCMx 37 on package pin 8 */
#define Huidu_IN6_PORT                                                   (GPIOA)
#define Huidu_IN6_PIN                                           (DL_GPIO_PIN_15)
#define Huidu_IN6_IOMUX                                          (IOMUX_PINCM37)
/* Defines for IN7: GPIOA.16 with pinCMx 38 on package pin 9 */
#define Huidu_IN7_PORT                                                   (GPIOA)
#define Huidu_IN7_PIN                                           (DL_GPIO_PIN_16)
#define Huidu_IN7_IOMUX                                          (IOMUX_PINCM38)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_0_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_I2C_0_init(void);
void SYSCFG_DL_UART_0_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
